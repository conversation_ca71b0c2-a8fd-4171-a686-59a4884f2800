import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { alias } from 'drizzle-orm/pg-core';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, asc, count, desc, eq, inArray, notInArray, gte, or, SQL, sql } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  users,
  professionals,
  specialists,
  workspaces,
  students,
  organisations,
  workspaceSubscriptions,
  reviews,
  employers,
  workspaceMembers,
  workspaceConnections,
  workspaceFollowers,
  workspaceInvitedUsers,
} from '@/db/schema';

import { EntityName } from '@/constants/entities';

import { BaseProfileDto } from './dto/base-profile.dto';
import { StudentBasicProfileDto } from './dto/student-profile.dto';
import { BasicAboutSectionDto } from './dto/update-about-section.dto';
import { SpecialistsProfileDto } from './dto/specialists-profile.dto';
import { OrganisationProfileDto } from './dto/organisation-profile.dto';
import { QualificationsSectionDto } from './dto/sections/qualifications-section.dto';
import { WorkExperienceDto } from './dto/sections/work-experience-section.dto';
import { ClinicalInterestDto } from './dto/sections/clinical-interest-section.dto';
import { AreaOfExpertiseDto } from './dto/sections/area-of-expertise-section.dto';
import { FAQDto } from './dto/sections/faq-section.dto';
import { ServicesDto } from './dto/sections/services-section.dto';
import { GetInTouchDto } from './dto/sections/get-in-touch-section.dto';
import { ShowcaseDto } from './dto/sections/showcases-section.dto';
import { FundraiserSectionDto } from './dto/sections/fundraiser-section.dto';
import { CustomSectionDto } from './dto/sections/custom-section.dto';
import { NewReviewDto } from './reviews/dto/new-review.dto';
import { FetchReviewsQueryDto } from './reviews/dto/get-reviews.dto';

import { itemNotFound } from '@/exceptions/common';
import { subscriptionDoesNotAllowVideosOrHighlights } from '@/exceptions/subscription';

import { removeUndefinedValues } from '@/utils/object';

import { UserSegment } from '@/constants/user-segments';
import { AccountType, UserStatus } from '@/constants/users';
import { SubscriptionPlan } from '@/constants/subscription-plans';
import { WorkspaceSubscriptionsStatus } from '@/constants/workspace-subscriptions';
import { DonationMethod, ProfileSection, ReviewSortOrder, ReviewStatus } from '@/constants/profile';
import { WorkspacesStatus, WORKSPACE_RELATIONS } from '@/constants/workspaces';
import { EntityType } from '@/constants/user-types';

import { NetworkingService } from '@/modules/networking/networking.service';
import { WorkspaceConnectionStatus } from '@/constants/networking';
import { FollowerStatus } from '@/constants/followers';
import { SuggestionParams, UserSuggestion } from './profile.controller';
import { TeamMemberManageDto } from './dto/add-members.dto';

export enum WorkspaceMemberStatus {
  PENDING = 1,
  ACCEPTED = 2,
  REJECTED = 3,
  REMOVED = 4, // When removed from workspace after accepting
}

export enum WorkspaceInvitedUserStatus {
  PENDING = 1,
  REGISTERED = 2, // When user registers and becomes workspace member
  CANCELLED = 3, // When invitation is cancelled before registration
}

@Injectable()
export class ProfileService {
  constructor(
    @Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>,
    private readonly networkingService: NetworkingService,
  ) {}

  findById(id: string, currentEntityId: string, entityType: EntityType) {
    return this.findUserByCondition(eq(users.id, id), currentEntityId, entityType);
  }

  async findAboutById(workspaceId: string) {
    const workspace = await this.db.query.workspaces.findFirst({
      where: eq(workspaces.id, workspaceId),
      columns: {
        highlights: true,
        custom: true,
        professionalOrOrganisationVideo: true,
        publicVideo: true,
        userSegment: true,
      },
      with: {
        createdByUser: {
          columns: {
            biography: true,
          },
        },
        [WORKSPACE_RELATIONS.ORGANISATION]: {
          columns: {
            showcases: true,
            fundraiser: true,
            getInTouch: true,
            services: true,
            faqs: true,
          },
        },
        [WORKSPACE_RELATIONS.PROFESSIONAL]: {
          columns: {
            showcases: true,
            fundraiser: true,
            getInTouch: true,
            faqs: true,
            areaOfExpertises: true,
            clinicalInterests: true,
            workExperiences: true,
            qualificationDetails: true,
          },
        },
        [WORKSPACE_RELATIONS.SPECIALISTS]: {
          columns: {
            showcases: true,
            fundraiser: true,
            getInTouch: true,
            faqs: true,
            areaOfExpertises: true,
            clinicalInterests: true,
            workExperiences: true,
            qualificationDetails: true,
          },
        },
        [WORKSPACE_RELATIONS.STUDENTS]: {
          columns: {
            areaOfExpertises: true,
            clinicalInterests: true,
            workExperiences: true,
            qualificationDetails: true,
          },
        },
      },
    });

    if (!workspace) throw itemNotFound(EntityName.WORKSPACE);

    const {
      specialists: specialist,
      professionals: professional,
      organisations: organisation,
      students: student,
      ...rest
    } = workspace;

    switch (workspace.userSegment) {
      case UserSegment.CARDIAC_SPECIALIST:
        return { ...rest, ...specialist };

      case UserSegment.ALLIED_CARDIAC:
        return { ...rest, ...professional };

      case UserSegment.ORGANISATION:
        return { ...rest, organisation };

      case UserSegment.STUDENT:
        return { ...rest, student };

      default:
        break;
    }
  }

  findByUsername(username: string, currentEntityId: string, entityType: EntityType) {
    return this.findUserByCondition(eq(users.username, username), currentEntityId, entityType);
  }

  private async findUserByCondition(
    condition: SQL,
    currentEntityId: string,
    entityType: EntityType,
  ) {
    const user = await this.db.query.users.findFirst({
      columns: {
        id: true,
        username: true,
        displayName: true,
        introductoryStatement: true,
        profileImageUrl: true,
        profileImageUrlThumbnail: true,
        coverPictureUrl: true,
        accountType: true,
        biography: true,
      },
      where: and(condition, eq(users.status, UserStatus.ACTIVE)),
    });

    if (!user) throw itemNotFound(EntityName.USER);

    let isOwnProfile = entityType === EntityType.USER && user.id === currentEntityId;

    let workspaceId;

    if ([AccountType.PROFESSIONAL, AccountType.ORGANISATION].includes(user.accountType)) {
      // Get workspace ID from the user's workspace users
      workspaceId = await this.db.query.workspaces
        .findFirst({
          columns: {
            id: true,
          },
          where: eq(workspaces.createdById, user.id),
        })
        .then((res) => res?.id);

      if (!workspaceId) throw itemNotFound(EntityName.WORKSPACE);

      isOwnProfile =
        isOwnProfile || (entityType === EntityType.WORKSPACE && workspaceId === currentEntityId);
    }

    // Get follower count
    const followerCount = workspaceId
      ? await this.networkingService.getTotalFollowersCount(workspaceId)
      : 0;

    // Get connection count
    const connectionCount = await this.networkingService.getTotalConnectionCount(user.id);

    // Check if current user is following this profile
    let isFollowing = false;
    if (currentEntityId && workspaceId && !isOwnProfile) {
      isFollowing = await this.networkingService.isFollowing(
        currentEntityId,
        workspaceId,
        entityType,
      );
    }

    // Check if current user is connected to this profile
    let isConnected = false;
    if (currentEntityId && !isOwnProfile) {
      // if the entity is workspace type, then taking it's user id, because, now the connection is belong to users table
      const entityId =
        entityType === EntityType.WORKSPACE
          ? await this.db.query.workspaces
              .findFirst({
                where: eq(workspaces.id, currentEntityId),
                columns: {
                  createdById: true,
                },
              })
              .then((res) => res?.createdById)
          : currentEntityId;

      isConnected = await this.networkingService.isConnected(entityId!, user.id);
    }

    // Get professional or specialist details based on account type
    let additionalDetails = {};
    let workspaceDetails = {};
    if (workspaceId) {
      const workspace = await this.db.query.workspaces.findFirst({
        columns: {
          userSegment: true,
          professionalOrOrganisationVideo: true,
          publicVideo: true,
          highlights: true,
          isRatingAndReviewEnabled: true,
        },
        where: eq(workspaces.id, workspaceId),
      });

      if (!workspace) throw itemNotFound(EntityName.WORKSPACE);

      workspaceDetails = {
        professionalOrOrganisationVideo: workspace.professionalOrOrganisationVideo,
        publicVideo: workspace.publicVideo,
        highlights: workspace.highlights,
        isRatingAndReviewEnabled: workspace.isRatingAndReviewEnabled,
      };

      if (user.accountType === AccountType.PROFESSIONAL) {
        if (workspace.userSegment === UserSegment.CARDIAC_SPECIALIST) {
          const professionalDetails = await this.db.query.specialists.findFirst({
            columns: {
              title: true,
              qualifications: true,
              jobTitle: true,
              jobTitleYear: true,
            },
            where: eq(specialists.workspaceId, workspaceId),
            with: {
              employer: {
                columns: {
                  name: true,
                },
              },
            },
          });
          if (!professionalDetails) throw itemNotFound(EntityName.WORKSPACE);
          additionalDetails = {
            title: professionalDetails.title,
            qualifications: professionalDetails.qualifications,
            designation: professionalDetails.jobTitle,
            jobTitleYear: professionalDetails.jobTitleYear,
            employer: professionalDetails.employer?.name,
          };
        } else if (workspace.userSegment === UserSegment.ALLIED_CARDIAC) {
          const professionalDetails = await this.db.query.professionals.findFirst({
            columns: {
              title: true,
              qualifications: true,
              designation: true,
              jobTitleYear: true,
            },
            where: eq(professionals.workspaceId, workspaceId),
            with: {
              employer: {
                columns: {
                  name: true,
                },
              },
            },
          });
          if (!professionalDetails) throw itemNotFound(EntityName.WORKSPACE);
          additionalDetails = {
            title: professionalDetails.title,
            qualifications: professionalDetails.qualifications,
            designation: professionalDetails.designation,
            jobTitleYear: professionalDetails.jobTitleYear,
            employer: professionalDetails.employer?.name,
          };
        } else if (workspace.userSegment === UserSegment.STUDENT) {
          const professionalDetails = await this.db.query.students.findFirst({
            columns: {
              institutionName: true,
            },
            where: eq(students.workspaceId, workspaceId),
          });
          if (!professionalDetails) throw itemNotFound(EntityName.WORKSPACE);
          additionalDetails = {
            institution: professionalDetails.institutionName,
          };
        }
      } else if (user.accountType === AccountType.ORGANISATION) {
        const organisationDetails = await this.db.query.organisations.findFirst({
          columns: {
            location: true,
          },
          where: eq(organisations.workspaceId, workspaceId),
          with: {
            segmentCategory: {
              columns: {
                name: true,
              },
            },
          },
        });
        if (!organisationDetails) throw itemNotFound(EntityName.WORKSPACE);

        additionalDetails = {
          category: organisationDetails.segmentCategory.name,
          location: organisationDetails.location,
        };
      }
    }

    return {
      username: user.username,
      displayName: user.displayName,
      introductoryStatement: user.introductoryStatement,
      profileImageUrl: user.profileImageUrl,
      profileImageUrlThumbnail: user.profileImageUrlThumbnail,
      coverPictureUrl: user.coverPictureUrl,
      accountType: user.accountType,
      biography: user.biography,
      followerCount,
      connectionCount,
      isFollowing,
      isConnected,
      isOwnProfile,
      ...additionalDetails,
      ...workspaceDetails,
    };
  }

  async updateBaseProfile(
    userId: string,
    updateProfileDto: BaseProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction ?? this.db;

    const [updatedUser] = await db
      .update(users)
      // Always update `updatedAt` so the timestamp refreshes,
      // even if `updateProfileDto` has no `users`-specific fields.
      .set({ ...updateProfileDto, updatedAt: sql`now()` })
      .where(and(eq(users.id, userId), eq(users.status, UserStatus.ACTIVE)))
      .returning();

    if (!updatedUser) throw itemNotFound(EntityName.USER);

    return updatedUser;
  }

  async updateAlliedCardiacBaseProfile(
    workspaceId: string,
    updateDto: SpecialistsProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction ?? this.db;

    // Map 'jobTitle' from the DTO to 'designation' in the database to avoid confusion.
    // This is necessary because the DTO uses 'jobTitle' while the database column is 'designation'.
    const { jobTitle, ...rest } = updateDto;

    const [updatedUser] = await db
      .update(professionals)
      .set({ ...rest, designation: jobTitle })
      .where(and(eq(professionals.workspaceId, workspaceId)))
      .returning();

    if (!updatedUser) throw itemNotFound(EntityName.ALLIED_CARDIAC);

    return updatedUser;
  }

  async updateSpecialistsBaseProfile(
    workspaceId: string,
    updateDto: SpecialistsProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction ?? this.db;

    const [updatedUser] = await db
      .update(specialists)
      .set(updateDto)
      .where(eq(specialists.workspaceId, workspaceId))
      .returning();

    if (!updatedUser) throw itemNotFound(EntityName.SPECIALIST);

    return updatedUser;
  }

  async updateStudentBasicProfile(
    workspaceId: string,
    updateDto: StudentBasicProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction ?? this.db;

    const [updatedUser] = await db
      .update(students)
      .set(updateDto)
      .where(eq(students.workspaceId, workspaceId))
      .returning();

    if (!updatedUser) throw itemNotFound(EntityName.STUDENT);

    return updatedUser;
  }

  async updateOrganisationBasicProfile(
    workspaceId: string,
    updateDto: OrganisationProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction ?? this.db;

    const [updatedUser] = await db
      .update(organisations)
      .set(updateDto)
      .where(eq(organisations.workspaceId, workspaceId))
      .returning();

    if (!updatedUser) throw itemNotFound(EntityName.ORGANISATION);

    return updatedUser;
  }

  async handleBasicSectionUpdate(
    userId: string,
    workspaceId: string,
    basicSection: BasicAboutSectionDto,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    const { biography, ...otherFields } = basicSection;

    // 1. Update biography in the users table
    if (biography !== undefined) {
      await txn.update(users).set({ biography }).where(eq(users.id, userId));
    }

    const cleanedFields = removeUndefinedValues(otherFields);

    // 2. Check for premium fields (videos or highlights) in the payload
    const containsPremiumFields = Object.keys(cleanedFields).length > 0;

    if (containsPremiumFields) {
      if (!workspaceId) throw itemNotFound(EntityName.WORKSPACE);

      const today = new Date();
      today.setUTCHours(0, 0, 0, 0); // sets time to 00:00 UTC

      // Verify that the workspace has an active subscription
      const activeSubscription = await txn.query.workspaceSubscriptions.findFirst({
        columns: {},
        where: and(
          eq(workspaceSubscriptions.workspaceId, workspaceId),
          eq(workspaceSubscriptions.status, WorkspaceSubscriptionsStatus.ACTIVE),
          gte(workspaceSubscriptions.endDate, today.toISOString()),
        ),
        with: {
          subscription: {
            columns: {
              title: true,
            },
          },
          workspace: {
            columns: {
              professionalOrOrganisationVideo: true,
              publicVideo: true,
            },
          },
        },
      });

      if (!activeSubscription) {
        throw itemNotFound(EntityName.SUBSCRIPTION);
      }

      // Reject update if subscription is BASIC
      if (activeSubscription.subscription.title === SubscriptionPlan.BASIC) {
        throw subscriptionDoesNotAllowVideosOrHighlights();
      }

      const workspaceUpdates = { ...cleanedFields };

      // BUSINESS LOGIC: If user only provides one video, assign it to publicVideo
      this.applyVideoManagementLogic(workspaceUpdates, activeSubscription.workspace);

      // Apply updates to workspace
      await txn.update(workspaces).set(workspaceUpdates).where(eq(workspaces.id, workspaceId));
    }
  }

  async handleQualificationSection(
    workspaceId: string,
    qualificationDetails: QualificationsSectionDto,
    segment: UserSegment.ALLIED_CARDIAC | UserSegment.CARDIAC_SPECIALIST | UserSegment.STUDENT,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    let tableToUpdate;
    switch (segment) {
      case UserSegment.ALLIED_CARDIAC:
        tableToUpdate = professionals;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        tableToUpdate = specialists;
        break;
      case UserSegment.STUDENT:
        tableToUpdate = students;
        break;
    }

    await txn
      .update(tableToUpdate)
      .set({ qualificationDetails })
      .where(eq(tableToUpdate.workspaceId, workspaceId));
  }

  async handleWorkExperienceSection(
    workspaceId: string,
    workExperiences: WorkExperienceDto[],
    segment: UserSegment.ALLIED_CARDIAC | UserSegment.CARDIAC_SPECIALIST | UserSegment.STUDENT,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    let tableToUpdate;
    switch (segment) {
      case UserSegment.ALLIED_CARDIAC:
        tableToUpdate = professionals;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        tableToUpdate = specialists;
        break;
      case UserSegment.STUDENT:
        tableToUpdate = students;
        break;
    }

    await txn
      .update(tableToUpdate)
      .set({ workExperiences })
      .where(eq(tableToUpdate.workspaceId, workspaceId));
  }

  async handleClinicalInterestSection(
    workspaceId: string,
    clinicalInterests: ClinicalInterestDto[],
    segment: UserSegment.ALLIED_CARDIAC | UserSegment.CARDIAC_SPECIALIST | UserSegment.STUDENT,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    let tableToUpdate;
    switch (segment) {
      case UserSegment.ALLIED_CARDIAC:
        tableToUpdate = professionals;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        tableToUpdate = specialists;
        break;
      case UserSegment.STUDENT:
        tableToUpdate = students;
        break;
    }

    await txn
      .update(tableToUpdate)
      .set({ clinicalInterests })
      .where(eq(tableToUpdate.workspaceId, workspaceId));
  }

  async handleAreaOfExpertiseSection(
    workspaceId: string,
    areaOfExpertises: AreaOfExpertiseDto[],
    segment: UserSegment.ALLIED_CARDIAC | UserSegment.CARDIAC_SPECIALIST | UserSegment.STUDENT,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    let tableToUpdate;
    switch (segment) {
      case UserSegment.ALLIED_CARDIAC:
        tableToUpdate = professionals;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        tableToUpdate = specialists;
        break;
      case UserSegment.STUDENT:
        tableToUpdate = students;
        break;
    }

    await txn
      .update(tableToUpdate)
      .set({ areaOfExpertises })
      .where(eq(tableToUpdate.workspaceId, workspaceId));
  }

  async handleFAQSection(
    workspaceId: string,
    faqs: FAQDto[],
    segment: UserSegment.ALLIED_CARDIAC | UserSegment.CARDIAC_SPECIALIST | UserSegment.ORGANISATION,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    let tableToUpdate;
    switch (segment) {
      case UserSegment.ALLIED_CARDIAC:
        tableToUpdate = professionals;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        tableToUpdate = specialists;
        break;
      case UserSegment.ORGANISATION:
        tableToUpdate = organisations;
        break;
    }

    await txn.update(tableToUpdate).set({ faqs }).where(eq(tableToUpdate.workspaceId, workspaceId));
  }

  async handleGetInTouchSection(
    workspaceId: string,
    getInTouch: GetInTouchDto,
    segment: UserSegment.ALLIED_CARDIAC | UserSegment.CARDIAC_SPECIALIST | UserSegment.ORGANISATION,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    let tableToUpdate;
    switch (segment) {
      case UserSegment.ALLIED_CARDIAC:
        tableToUpdate = professionals;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        tableToUpdate = specialists;
        break;
      case UserSegment.ORGANISATION:
        tableToUpdate = organisations;
        break;
    }

    await txn
      .update(tableToUpdate)
      .set({ getInTouch })
      .where(eq(tableToUpdate.workspaceId, workspaceId));
  }

  async handleShowcaseSection(
    workspaceId: string,
    showcases: ShowcaseDto[],
    segment: UserSegment.ALLIED_CARDIAC | UserSegment.CARDIAC_SPECIALIST | UserSegment.ORGANISATION,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    let tableToUpdate;
    switch (segment) {
      case UserSegment.ALLIED_CARDIAC:
        tableToUpdate = professionals;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        tableToUpdate = specialists;
        break;
      case UserSegment.ORGANISATION:
        tableToUpdate = organisations;
        break;
    }

    await txn
      .update(tableToUpdate)
      .set({ showcases })
      .where(eq(tableToUpdate.workspaceId, workspaceId));
  }

  async handleFundraiserSection(
    workspaceId: string,
    fundraiser: FundraiserSectionDto,
    segment: UserSegment.ALLIED_CARDIAC | UserSegment.CARDIAC_SPECIALIST | UserSegment.ORGANISATION,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    let tableToUpdate;
    switch (segment) {
      case UserSegment.ALLIED_CARDIAC:
        tableToUpdate = professionals;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        tableToUpdate = specialists;
        break;
      case UserSegment.ORGANISATION:
        tableToUpdate = organisations;
        break;
    }

    // TODO: Add a check to verify if a bank account exists once that functionality is implemented.
    // Currently, we do not allow the MiniCardiac Donation Service option since no bank account is linked to the user.
    if (fundraiser.donationMethod === DonationMethod.MINICARDIAC_SERVICE) {
      throw new BadRequestException(
        'You cannot use the MiniCardiac Donation Service since no bank details are available.',
      );
    }

    await txn
      .update(tableToUpdate)
      .set({ fundraiser })
      .where(eq(tableToUpdate.workspaceId, workspaceId));
  }

  async handleCustomSection(
    workspaceId: string,
    custom: CustomSectionDto,
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    await txn.update(workspaces).set({ custom }).where(eq(workspaces.id, workspaceId));
  }

  async handleServicesSection(
    workspaceId: string,
    services: ServicesDto[],
    txn: PostgresJsDatabase<typeof schema>,
  ) {
    await txn
      .update(organisations)
      .set({ services })
      .where(eq(organisations.workspaceId, workspaceId));
  }

  /**
   * Applies business logic for video management to workspace updates.
   * If user only provides one video, assign it to publicVideo.
   */
  private applyVideoManagementLogic(
    workspaceUpdates: Omit<BasicAboutSectionDto, 'biography'>,
    workspace: { professionalOrOrganisationVideo?: string | null },
  ): void {
    if (workspaceUpdates.publicVideo === null) {
      if (workspaceUpdates.professionalOrOrganisationVideo === undefined) {
        const existingProOrgVideo = workspace.professionalOrOrganisationVideo;
        if (existingProOrgVideo) {
          workspaceUpdates.publicVideo = existingProOrgVideo;
          workspaceUpdates.professionalOrOrganisationVideo = null;
        }
      } else if (workspaceUpdates.professionalOrOrganisationVideo !== null) {
        workspaceUpdates.publicVideo = workspaceUpdates.professionalOrOrganisationVideo;
        workspaceUpdates.professionalOrOrganisationVideo = null;
      }
    }
  }

  async getUserSegmentAndSubscriptionStatus(workspaceId: string) {
    const user = await this.db
      .select({
        userId: workspaces.createdById,
        accountType: users.accountType,
        userSegment: workspaces.userSegment,
      })
      .from(workspaces)
      .innerJoin(users, eq(users.id, workspaces.createdById))
      .where(
        and(
          eq(workspaces.id, workspaceId),
          gte(workspaces.status, WorkspacesStatus.PENDING), // TODO: Once we have option to admin side for approving the workspace, we may have to update here as well
          eq(users.status, UserStatus.ACTIVE),
        ),
      )
      .limit(1)
      .then((res) => res[0]);

    if (!user) {
      throw itemNotFound(EntityName.USER);
    }

    const isSurgeonOrCardiologist = [
      UserSegment.ALLIED_CARDIAC,
      UserSegment.CARDIAC_SPECIALIST,
    ].includes(user.userSegment);

    let isPremium = false;

    if (isSurgeonOrCardiologist) {
      const today = new Date();
      today.setUTCHours(0, 0, 0, 0);

      const activeSubscription = await this.db.query.workspaceSubscriptions.findFirst({
        columns: {},
        where: and(
          eq(workspaceSubscriptions.workspaceId, workspaceId),
          eq(workspaceSubscriptions.status, WorkspaceSubscriptionsStatus.ACTIVE),
          gte(workspaceSubscriptions.endDate, today.toISOString()),
        ),
        with: {
          subscription: {
            columns: { title: true },
          },
        },
      });

      if (!activeSubscription) {
        throw itemNotFound(EntityName.SUBSCRIPTION);
      }

      isPremium = activeSubscription.subscription.title !== SubscriptionPlan.BASIC;
    }

    return { segment: user.userSegment, isPremium };
  }

  getSectionDetails(userSegment: UserSegment, isPremium: boolean) {
    switch (userSegment) {
      case UserSegment.STUDENT:
        return [
          ProfileSection.QUALIFICATION,
          ProfileSection.WORK_EXPERIENCE,
          ProfileSection.CLINICAL_INTEREST,
          ProfileSection.AREA_OF_EXPERTISE,
          ProfileSection.CUSTOM,
        ];

      case UserSegment.ALLIED_CARDIAC:
      case UserSegment.CARDIAC_SPECIALIST:
        return isPremium
          ? [
              ProfileSection.QUALIFICATION,
              ProfileSection.WORK_EXPERIENCE,
              ProfileSection.CLINICAL_INTEREST,
              ProfileSection.AREA_OF_EXPERTISE,
              ProfileSection.CUSTOM,
              ProfileSection.FAQ,
              ProfileSection.TEAM,
              ProfileSection.SHOWCASE,
              ProfileSection.GET_IN_TOUCH,
              ProfileSection.FUNDRAISER,
            ]
          : [
              ProfileSection.QUALIFICATION,
              ProfileSection.WORK_EXPERIENCE,
              ProfileSection.CLINICAL_INTEREST,
              ProfileSection.AREA_OF_EXPERTISE,
              ProfileSection.CUSTOM,
              ProfileSection.FAQ,
            ];

      case UserSegment.ORGANISATION:
        return [
          ProfileSection.FAQ,
          ProfileSection.TEAM,
          ProfileSection.SHOWCASE,
          ProfileSection.SERVICE,
          ProfileSection.GET_IN_TOUCH,
          ProfileSection.FUNDRAISER,
          ProfileSection.CUSTOM,
        ];

      default:
        return [];
    }
  }

  isSectionsAllowed(userSegment: UserSegment, isPremium: boolean, sections: ProfileSection[]) {
    const userAllowedSections = this.getSectionDetails(userSegment, isPremium);

    return sections.every((section) => userAllowedSections.includes(section));
  }

  async createReview(
    reviewData: NewReviewDto,
    workspaceId: string,
    entityId: string,
    entityType: EntityType,
  ) {
    const [newReview] = await this.db
      .insert(reviews)
      .values({
        ...reviewData,
        workspaceId,
        entityId,
        entityType,
        status: ReviewStatus.ACTIVE,
      })
      .returning();

    return newReview.id;
  }

  async getReviews({
    workspaceId,
    limit,
    offset,
    sortOrder,
  }: {
    workspaceId: string;
  } & FetchReviewsQueryDto) {
    const userAlias = alias(users, 'u');
    const workspaceAlias = alias(workspaces, 'w');
    const workspaceCreatorAlias = alias(users, 'wu');

    const sortOrderSql: SQL[] = [desc(reviews.createdAt)];

    switch (sortOrder) {
      case ReviewSortOrder.HIGHEST:
        sortOrderSql.unshift(desc(reviews.rating));
        break;

      case ReviewSortOrder.LOWEST:
        sortOrderSql.unshift(asc(reviews.rating));
        break;

      default:
        break;
    }

    const reviewDatas = await this.db
      .select({
        id: reviews.id,
        message: reviews.message,
        rating: reviews.rating,
        createdAt: reviews.createdAt,

        // Creator info
        userDisplayName: userAlias.displayName,
        userUsername: userAlias.username,
        userProfileImageUrlThumbnail: userAlias.profileImageUrlThumbnail,

        workspaceDisplayName: workspaceCreatorAlias.displayName,
        workspaceUsername: workspaceCreatorAlias.username,
        workspaceProfileImageUrlThumbnail: workspaceCreatorAlias.profileImageUrlThumbnail,

        entityType: reviews.entityType,
      })
      .from(reviews)

      // Review creator (user)
      .leftJoin(
        userAlias,
        and(
          eq(userAlias.id, reviews.entityId),
          eq(reviews.entityType, EntityType.USER),
          eq(userAlias.status, UserStatus.ACTIVE),
        ),
      )

      // Review creator (workspace)
      .leftJoin(
        workspaceAlias,
        and(
          eq(workspaceAlias.id, reviews.entityId),
          eq(reviews.entityType, EntityType.WORKSPACE),
          // eq(workspaceAlias.status, WorkspacesStatus.ACTIVE),
        ),
      )

      // Workspace's creator
      .leftJoin(
        workspaceCreatorAlias,
        and(
          eq(workspaceCreatorAlias.id, workspaceAlias.createdById),
          eq(workspaceCreatorAlias.status, UserStatus.ACTIVE),
        ),
      )

      // WHERE clause
      .where(
        and(
          eq(reviews.workspaceId, workspaceId),
          eq(reviews.status, ReviewStatus.ACTIVE),

          // Only include review if creator is valid
          or(
            and(eq(reviews.entityType, EntityType.USER), eq(userAlias.status, UserStatus.ACTIVE)),
            and(
              eq(reviews.entityType, EntityType.WORKSPACE),
              // eq(workspaceAlias.status, WorkspacesStatus.ACTIVE),
              eq(workspaceCreatorAlias.status, UserStatus.ACTIVE),
            ),
          ),
        ),
      )

      .groupBy(reviews.id, userAlias.id, workspaceCreatorAlias.id)

      .orderBy(...sortOrderSql)
      .limit(limit)
      .offset((offset - 1) * limit);

    type ReviewDataOne = (typeof reviewDatas)[0];

    return reviewDatas.map((rd) => {
      const isUser = rd.entityType === EntityType.USER;

      const formatterData: Partial<ReviewDataOne> = rd;

      const creator = {
        displayName: isUser ? rd.userDisplayName : rd.workspaceDisplayName,
        username: isUser ? rd.userUsername : rd.workspaceUsername,
        profileImageUrlThumbnail: isUser
          ? rd.userProfileImageUrlThumbnail
          : rd.workspaceProfileImageUrlThumbnail,
      };

      delete formatterData.userDisplayName;
      delete formatterData.userProfileImageUrlThumbnail;
      delete formatterData.userUsername;
      delete formatterData.workspaceDisplayName;
      delete formatterData.workspaceProfileImageUrlThumbnail;
      delete formatterData.workspaceUsername;

      return {
        ...formatterData,
        ...creator,
      };
    });
  }

  async getWorkspaceMemberSuggestions({
    workspaceId,
    excludeIds,
    searchKeyword,
    limit,
    offset,
  }: SuggestionParams) {
    // First, get workspace details to determine type and organization
    const workspaceDetails = await this.db.query.workspaces.findFirst({
      columns: {
        id: true,
        userSegment: true,
      },
      where: eq(workspaces.id, workspaceId),
      with: {
        organisations: {
          columns: {
            id: true,
          },
        },
      },
    });

    if (!workspaceDetails) {
      throw itemNotFound(EntityName.WORKSPACE);
    }

    // Get existing members to exclude
    const existingMembers = await this.db
      .select({ memberWorkspaceId: workspaceMembers.memberWorkspaceId })
      .from(workspaceMembers)
      .where(
        and(
          eq(workspaceMembers.workspaceId, workspaceId),
          inArray(workspaceMembers.status, [
            WorkspaceMemberStatus.ACCEPTED,
            WorkspaceMemberStatus.PENDING,
          ]),
        ),
      );

    const existingMemberWorkspaceIds = existingMembers.map((m) => m.memberWorkspaceId);

    // Get workspace IDs of existing members to exclude their users
    const existingMemberUsers =
      existingMemberWorkspaceIds.length > 0
        ? await this.db
            .select({ createdById: workspaces.createdById })
            .from(workspaces)
            .where(inArray(workspaces.id, existingMemberWorkspaceIds))
        : [];

    const excludeAllUserIds = [
      ...(excludeIds?.length ? excludeIds : []),
      ...existingMemberUsers.map((u) => u.createdById),
    ];

    const isOrganisation = workspaceDetails.userSegment === UserSegment.ORGANISATION;
    const organisationId = workspaceDetails.organisations?.id;

    // Normalize search keyword
    const normalizedKeyword = searchKeyword?.toLowerCase().replace(/\s+/g, '');

    // Base search conditions
    const searchConditions = normalizedKeyword
      ? sql`LOWER(REPLACE(${users.displayName}, ' ', '')) LIKE ${`%${normalizedKeyword}%`}`
      : sql`TRUE`;

    // Only consider cardiac specialists and allied cardiac professionals
    const validUserSegments = [UserSegment.CARDIAC_SPECIALIST, UserSegment.ALLIED_CARDIAC];

    // Build suggestions with priorities
    const suggestions: UserSuggestion[] = [];

    if (isOrganisation && organisationId) {
      // Priority 1: Users from same organization (for organisation workspaces)
      const sameOrgUsers = await this.db
        .selectDistinct({
          id: users.id,
          workspaceId: workspaces.id,
          displayName: users.displayName,
          username: users.username,
          email: users.email,
          profileImageUrlThumbnail: users.profileImageUrlThumbnail,
        })
        .from(users)
        .innerJoin(workspaces, eq(workspaces.createdById, users.id))
        .innerJoin(professionals, eq(professionals.workspaceId, workspaces.id))
        .innerJoin(employers, eq(employers.id, professionals.employerId))
        .where(
          and(
            eq(employers.organisationId, organisationId),
            eq(users.status, UserStatus.ACTIVE),
            gte(workspaces.status, WorkspacesStatus.PENDING), // TODO: once the admin side for workspace approving page done
            inArray(workspaces.userSegment, validUserSegments),
            excludeAllUserIds.length > 0 ? notInArray(users.id, excludeAllUserIds) : sql`TRUE`,
            searchConditions,
          ),
        )
        .limit(limit)
        .offset(offset);

      suggestions.push(
        ...sameOrgUsers.map((user) => ({
          ...user,
          priority: 1,
          reason: 'same_organization' as const,
        })),
      );
    }

    // If we still need more suggestions or it's a professional workspace
    const remainingLimit = limit - suggestions.length;

    if (remainingLimit > 0) {
      // Priority 2: Connected users
      const connectedUsers = await this.db
        .selectDistinct({
          id: users.id,
          displayName: users.displayName,
          worksapceId: workspaces.id,
          username: users.username,
          email: users.email,
          profileImageUrlThumbnail: users.profileImageUrlThumbnail,
        })
        .from(users)
        .innerJoin(workspaces, eq(workspaces.createdById, users.id))
        .innerJoin(
          workspaceConnections,
          or(
            and(
              eq(workspaceConnections.recipientId, workspaceId),
              eq(workspaceConnections.requestorId, workspaces.id),
            ),
            and(
              eq(workspaceConnections.requestorId, workspaceId),
              eq(workspaceConnections.recipientId, workspaces.id),
            ),
          ),
        )
        .where(
          and(
            eq(workspaceConnections.status, WorkspaceConnectionStatus.APPROVED),
            eq(users.status, UserStatus.ACTIVE),
            gte(workspaces.status, WorkspacesStatus.PENDING), // TODO: once the admin side for workspace approving page done
            inArray(workspaces.userSegment, validUserSegments),
            excludeAllUserIds.length > 0 ? notInArray(users.id, excludeAllUserIds) : sql`TRUE`,
            searchConditions,
          ),
        )
        .limit(remainingLimit)
        .offset(suggestions.length > 0 ? 0 : offset);

      suggestions.push(
        ...connectedUsers.map((user) => ({
          ...user,
          priority: isOrganisation ? 2 : 1,
          reason: 'connection' as const,
        })),
      );
    }

    // Priority 3: Followers
    const remainingLimit2 = limit - suggestions.length;

    if (remainingLimit2 > 0) {
      const followerUsers = await this.db
        .selectDistinct({
          id: users.id,
          displayName: users.displayName,
          workspaceId: workspaces.id,
          username: users.username,
          email: users.email,
          profileImageUrlThumbnail: users.profileImageUrlThumbnail,
        })
        .from(users)
        .innerJoin(workspaces, eq(workspaces.createdById, users.id))
        .innerJoin(workspaceFollowers, eq(workspaceFollowers.entityId, workspaces.id))
        .where(
          and(
            // eq(workspaceFollowers.followingWorkspaceId, workspaceId),
            eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
            eq(users.status, UserStatus.ACTIVE),
            gte(workspaces.status, WorkspacesStatus.PENDING), // TODO: once the admin side for workspace approving page done
            inArray(workspaces.userSegment, validUserSegments),
            excludeAllUserIds.length > 0 ? notInArray(users.id, excludeAllUserIds) : sql`TRUE`,
            searchConditions,
          ),
        )
        .limit(remainingLimit2)
        .offset(suggestions.length > 0 ? 0 : offset);

      suggestions.push(
        ...followerUsers.map((user) => ({
          ...user,
          priority: isOrganisation ? 3 : 2,
          reason: 'follower' as const,
        })),
      );
    }

    // Priority 4: Any other users
    const remainingLimit3 = limit - suggestions.length;

    if (remainingLimit3 > 0) {
      const existingUserIds = suggestions.map((s) => s.id);

      const otherUsers = await this.db
        .selectDistinct({
          id: users.id,
          displayName: users.displayName,
          workspaceId: workspaces.id,
          username: users.username,
          email: users.email,
          profileImageUrlThumbnail: users.profileImageUrlThumbnail,
        })
        .from(users)
        .innerJoin(workspaces, eq(workspaces.createdById, users.id))
        .where(
          and(
            eq(users.status, UserStatus.ACTIVE),
            gte(workspaces.status, WorkspacesStatus.PENDING),
            inArray(workspaces.userSegment, validUserSegments),
            excludeAllUserIds.length > 0 ? notInArray(users.id, excludeAllUserIds) : sql`TRUE`,
            existingUserIds.length > 0 ? notInArray(users.id, existingUserIds) : sql`TRUE`,
            searchConditions,
          ),
        )
        .limit(remainingLimit3)
        .offset(suggestions.length > 0 ? 0 : offset)
        .orderBy(users.displayName);

      suggestions.push(
        ...otherUsers.map((user) => ({
          ...user,
          priority: isOrganisation ? 4 : 3,
          reason: 'other' as const,
        })),
      );
    }

    // Sort by priority and return
    return suggestions.sort((a, b) => a.priority - b.priority).slice(0, limit);
  }

  async manageTeamMembers({
    workspaceId,
    workspaceIds,
    newUsers,
    // autoConnectOnAccept,
  }: TeamMemberManageDto & {
    workspaceId: string;
  }) {
    return this.db.transaction(async (trx) => {
      // 1. Validate all workspace IDs exist and are active
      const validWorkspaces = workspaceIds.length
        ? await trx.query.workspaces.findMany({
            columns: { id: true },
            where: and(
              inArray(workspaces.id, workspaceIds),
              gte(workspaces.status, WorkspacesStatus.PENDING), // update this to only active once the admin workspace manage page is there
              inArray(workspaces.userSegment, [
                UserSegment.CARDIAC_SPECIALIST,
                UserSegment.ALLIED_CARDIAC,
              ]),
            ),
          })
        : [];

      if (validWorkspaces.length !== workspaceIds.length) {
        const invalidIds = workspaceIds.filter((id) => !validWorkspaces.some((w) => w.id === id));
        throw new Error(`Invalid workspace IDs: ${invalidIds.join(', ')}`);
      }

      // 2. Get current state of members and invitations
      const currentMembers = await trx
        .select({
          memberWorkspaceId: workspaceMembers.memberWorkspaceId,
          status: workspaceMembers.status,
        })
        .from(workspaceMembers)
        .where(eq(workspaceMembers.workspaceId, workspaceId));

      const currentInvitations = await trx
        .select({
          id: workspaceInvitedUsers.id,
          email: workspaceInvitedUsers.email,
          tempName: workspaceInvitedUsers.tempName,
          tempRole: workspaceInvitedUsers.tempRole,
          status: workspaceInvitedUsers.status,
          autoConnectOnAccept: workspaceInvitedUsers.autoConnectOnAccept,
        })
        .from(workspaceInvitedUsers)
        .where(
          and(
            eq(workspaceInvitedUsers.workspaceId, workspaceId),
            inArray(workspaceInvitedUsers.status, [
              WorkspaceInvitedUserStatus.PENDING,
              WorkspaceInvitedUserStatus.REGISTERED,
            ]),
          ),
        );

      // 3. Process workspace members
      const currentMemberIds = currentMembers.map((m) => m.memberWorkspaceId);
      const newMemberIds = workspaceIds;

      // Members to remove (not in new list)
      const membersToRemove = currentMemberIds.filter((id) => !newMemberIds.includes(id));

      // Members to add (in new list but not current)
      const membersToAdd = newMemberIds.filter((id) => !currentMemberIds.includes(id));

      // Remove members (update status instead of delete for audit trail)
      if (membersToRemove.length > 0) {
        await trx
          .update(workspaceMembers)
          .set({
            status: WorkspaceMemberStatus.REMOVED,
          })
          .where(
            and(
              eq(workspaceMembers.workspaceId, workspaceId),
              inArray(workspaceMembers.memberWorkspaceId, membersToRemove),
            ),
          );
      }

      // Add new members
      if (membersToAdd.length > 0) {
        const newMemberInserts = membersToAdd.map((memberWorkspaceId) => ({
          workspaceId,
          memberWorkspaceId,
          status: WorkspaceMemberStatus.PENDING,
          // autoConnectOnAccept,
        }));

        await trx.insert(workspaceMembers).values(newMemberInserts);
      }

      // 4. Process new user invitations
      const currentInvitationEmails = currentInvitations.map((i) => i.email.toLowerCase());
      const newUserEmails = newUsers.map((u) => u.email.toLowerCase());

      // Cancel invitations that are no longer in the list
      const invitationsToCancel = currentInvitations.filter(
        (inv) => !newUserEmails.includes(inv.email.toLowerCase()),
      );

      if (invitationsToCancel.length > 0) {
        await trx
          .update(workspaceInvitedUsers)
          .set({
            status: WorkspaceInvitedUserStatus.CANCELLED,
          })
          .where(
            inArray(
              workspaceInvitedUsers.id,
              invitationsToCancel.map((i) => i.id),
            ),
          );
      }

      // Process new users
      for (const newUser of newUsers) {
        const normalizedEmail = newUser.email.toLowerCase();

        // Check if invitation already exists
        const existingInvitation = currentInvitations.find(
          (inv) => inv.email.toLowerCase() === normalizedEmail,
        );

        if (existingInvitation) {
          // If invitation was already registered, skip the duplicate user check
          if (existingInvitation.status === WorkspaceInvitedUserStatus.REGISTERED) {
            // Just update the details, don't change status back to PENDING
            const needsUpdate =
              existingInvitation.tempName !== newUser.name ||
              existingInvitation.tempRole !== newUser.role;

            if (needsUpdate) {
              await trx
                .update(workspaceInvitedUsers)
                .set({
                  tempName: newUser.name,
                  tempRole: newUser.role,
                  // Don't change status or autoConnect for registered users
                })
                .where(eq(workspaceInvitedUsers.id, existingInvitation.id));
            }
          } else {
            // Update pending invitation
            const needsUpdate =
              existingInvitation.tempName !== newUser.name ||
              existingInvitation.tempRole !== newUser.role;

            if (needsUpdate) {
              await trx
                .update(workspaceInvitedUsers)
                .set({
                  tempName: newUser.name,
                  tempRole: newUser.role,
                  status: WorkspaceInvitedUserStatus.PENDING,
                  // autoConnectOnAccept, // Only for pending invitations
                  updatedAt: new Date(),
                })
                .where(eq(workspaceInvitedUsers.id, existingInvitation.id));
            }
          }
        } else {
          // Check if user already exists in system (only for new invitations)
          const existingUser = await trx.query.users.findFirst({
            columns: { id: true, email: true },
            where: eq(users.email, normalizedEmail),
          });

          if (existingUser) {
            throw new Error(`User with email ${newUser.email} already exists in the system`);
          }

          // Create new invitation
          // await trx.insert(workspaceInvitedUsers).values({
          //   workspaceId,
          //   email: normalizedEmail,
          //   tempName: newUser.name,
          //   tempRole: newUser.role,
          //   status: WorkspaceInvitedUserStatus.PENDING,
          //   // autoConnectOnAccept, // Only for new invitations
          // });
        }
      }

      // 5. Return summary of changes
      return {
        membersAdded: membersToAdd.length,
        membersRemoved: membersToRemove.length,
        invitationsCreated: newUsers.filter(
          (u) => !currentInvitationEmails.includes(u.email.toLowerCase()),
        ).length,
        invitationsUpdated: newUsers.filter((u) =>
          currentInvitationEmails.includes(u.email.toLowerCase()),
        ).length,
        invitationsCancelled: invitationsToCancel.length,
      };
    });
  }

  async getAverageReview(workspaceId: string) {
    return this.db
      .select({
        average: sql<number>`ROUND(AVG(${reviews.rating}), 1)`, // DB rounds to 1 decimal
        total: count().mapWith(Number),
      })
      .from(reviews)
      .where(and(eq(reviews.workspaceId, workspaceId), eq(reviews.status, ReviewStatus.ACTIVE)))
      .then((val) => ({
        average: val[0].average,
        total: val[0].total,
      }));
  }
  // Helper method to get team overview
  // async getTeamOverview(workspaceId: string) {
  //   // Active workspace members
  //   const members = await this.db
  //     .select({
  //       memberWorkspaceId: workspaceMembers.memberWorkspaceId,
  //       status: workspaceMembers.status,
  //       createdAt: workspaceMembers.createdAt,
  //       // Get member workspace details
  //       memberDisplayName: sql`${users.displayName}`.as('member_display_name'),
  //       memberUsername: sql`${users.username}`.as('member_username'),
  //       memberProfileImage: sql`${users.profileImageUrlThumbnail}`.as('member_profile_image'),
  //     })
  //     .from(workspaceMembers)
  //     .innerJoin(workspaces, eq(workspaces.id, workspaceMembers.memberWorkspaceId))
  //     .innerJoin(users, eq(users.id, workspaces.createdById))
  //     .where(
  //       and(
  //         eq(workspaceMembers.workspaceId, workspaceId),
  //         gte(workspaceMembers.status, WorkspaceMemberStatus.PENDING), // All active statuses
  //       ),
  //     );

  //   // Pending invitations
  //   const invitations = await this.db
  //     .select()
  //     .from(workspaceInvitedUsers)
  //     .where(
  //       and(
  //         eq(workspaceInvitedUsers.workspaceId, workspaceId),
  //         eq(workspaceInvitedUsers.status, WorkspaceInvitedUserStatus.PENDING),
  //       ),
  //     );

  //   return {
  //     members,
  //     pendingInvitations: invitations,
  //     totalMembers: members.filter((m) => m.status === WorkspaceMemberStatus.ACCEPTED).length,
  //     pendingMembers: members.filter((m) => m.status === WorkspaceMemberStatus.PENDING).length,
  //     pendingInvitationsCount: invitations.length,
  //   };
  // }

  // // Method to handle when invited user registers
  // handleUserRegistration(email: string, newUserId: string) {
  //   return this.db.transaction(async (trx) => {
  //     // Find pending invitations for this email
  //     const pendingInvitations = await trx
  //       .select()
  //       .from(workspaceInvitedUsers)
  //       .where(
  //         and(
  //           eq(workspaceInvitedUsers.email, email.toLowerCase()),
  //           eq(workspaceInvitedUsers.status, WorkspaceInvitedUserStatus.PENDING),
  //         ),
  //       );

  //     for (const invitation of pendingInvitations) {
  //       // Create workspace member entry
  //       await trx.insert(workspaceMembers).values({
  //         workspaceId: invitation.workspaceId,
  //         memberWorkspaceId: newUserId, // This should be the new user's workspace ID
  //         status: WorkspaceMemberStatus.ACCEPTED,
  //       });

  //       // Update invitation status
  //       await trx
  //         .update(workspaceInvitedUsers)
  //         .set({
  //           status: WorkspaceInvitedUserStatus.REGISTERED,
  //           updatedAt: new Date(),
  //         })
  //         .where(eq(workspaceInvitedUsers.id, invitation.id));

  //       // Handle auto-connect if enabled
  //       if (invitation.autoConnectOnAccept) {
  //         // Create connection request or auto-accept
  //         // Implementation depends on your connection system
  //         await this.createAutoConnection(invitation.workspaceId, newUserId);
  //       }
  //     }
  //   });
  // }

  // private async createAutoConnection(invitingWorkspaceId: string, newUserWorkspaceId: string) {
  //   // Implementation depends on your workspace connection system
  //   // This could create a pending connection request or auto-accept it

  //   const existingConnection = await this.db.query.workspaceConnections.findFirst({
  //     where: or(
  //       and(
  //         eq(workspaceConnections.recipientId, invitingWorkspaceId),
  //         eq(workspaceConnections.requestorId, newUserWorkspaceId),
  //       ),
  //       and(
  //         eq(workspaceConnections.requestorId, newUserWorkspaceId),
  //         eq(workspaceConnections.recipientId, invitingWorkspaceId),
  //       ),
  //     ),
  //   });

  //   if (!existingConnection) {
  //     await this.db.insert(workspaceConnections).values({
  //       requestorId: invitingWorkspaceId,
  //       requestedWorkspaceId: newUserWorkspaceId,
  //       status: ConnectionStatus.ACCEPTED, // Auto-accept since invited
  //     });
  //   }
  // }
}
