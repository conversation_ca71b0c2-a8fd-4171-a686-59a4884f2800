import { IsOptional, IsString, IsUUID, Min<PERSON>ength } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { BaseProfileDto } from './base-profile.dto';

export class OrganisationProfileDto extends BaseProfileDto {
  @ApiProperty({
    name: 'location',
    type: 'string',
    required: false,
    example: 'New York, USA',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(1)
  locationId?: string;

  @ApiProperty({
    name: 'location',
    type: 'string',
    required: false,
    example: 'New York, USA',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(1)
  location?: string;

  @ApiProperty({
    name: 'categoryId',
    type: 'string',
    required: false,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;
}
