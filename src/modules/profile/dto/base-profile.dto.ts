import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

export class BaseProfileDto {
  @ApiProperty({
    name: 'displayName',
    type: 'string',
    required: false,
    example: 'john doe',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(1)
  displayName?: string;

  @ApiProperty({
    name: 'introductoryStatement',
    type: 'string',
    required: false,
    example: 'Cardiologist with 10 years of experience',
  })
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MaxLength(240, { message: 'maximum 240 characters only' })
  @MinLength(1)
  @IsString()
  introductoryStatement?: string;

  @ApiProperty({
    name: 'profileImageUrl',
    type: 'string',
    required: false,
    example: 'public/image-url.com',
  })
  @IsString()
  @IsOptional()
  profileImageUrl?: string;

  @ApiProperty({
    name: 'profileImageUrlThumbnail',
    type: 'string',
    required: false,
    example: 'public/image-url.com',
  })
  @IsString()
  @IsOptional()
  profileImageUrlThumbnail?: string;

  @ApiProperty({
    name: 'coverPictureUrl',
    type: 'string',
    required: false,
    example: 'public/image-url.com',
  })
  @IsString()
  @IsOptional()
  coverPictureUrl?: string;
}
