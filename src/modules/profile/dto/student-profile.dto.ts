import { IsOptional, IsString, MinLength } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { BaseProfileDto } from './base-profile.dto';

export class StudentBasicProfileDto extends BaseProfileDto {
  @ApiProperty({
    name: 'institutionName',
    type: 'string',
    required: false,
    example: 'Harvard Medical School',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(1)
  institutionName: string;
}
