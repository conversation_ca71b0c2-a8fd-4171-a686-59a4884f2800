import {
  IsString,
  Is<PERSON>ptional,
  IsA<PERSON>y,
  ValidateNested,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  ArrayMaxSize,
  IsObject,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON><PERSON>th,
  ValidateIf,
} from 'class-validator';
import { Type, Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional, getSchemaPath, ApiExtraModels } from '@nestjs/swagger';

import { HighlightType } from '@/constants/workspaces';
import {
  MAX_AREA_OF_EXPERTISE_ITEMS,
  MAX_CLINICAL_INTEREST_ITEMS,
  MAX_FAQ_ITEMS,
  MAX_PROFILE_TEXT_LENGTH,
  MAX_SERVICES_ITEMS,
  MAX_SHOWCASE_ITEMS,
  MAX_WORK_EXPERIENCE_ITEMS,
  MIN_PROFILE_TEXT_LENGTH,
  ProfileSection,
} from '@/constants/profile';

import { QualificationsSectionDto } from './sections/qualifications-section.dto';
import { WorkExperienceDto } from './sections/work-experience-section.dto';
import { AreaOfExpertiseDto } from './sections/area-of-expertise-section.dto';
import { ClinicalInterestDto } from './sections/clinical-interest-section.dto';
import { FAQDto } from './sections/faq-section.dto';
import { ServicesDto } from './sections/services-section.dto';
import { GetInTouchDto } from './sections/get-in-touch-section.dto';
import { ShowcaseDto } from './sections/showcases-section.dto';
import { FundraiserSectionDto } from './sections/fundraiser-section.dto';
import { CustomSectionDto } from './sections/custom-section.dto';

// ---------- Highlights DTOs ----------
class BaseHighlight {
  @ApiProperty({
    enum: HighlightType,
    description: 'Type of highlight',
  })
  @IsEnum(HighlightType)
  type: HighlightType;
}

export class AwardHighlightDto extends BaseHighlight {
  @ApiProperty({ enum: [HighlightType.AWARD], example: HighlightType.AWARD })
  readonly type = HighlightType.AWARD;

  @ApiProperty({ example: 'Best Innovation Award' })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  title: string;

  @ApiProperty({ example: 2023 })
  @IsNumber()
  year: number;

  @ApiProperty({ example: 'Tech University' })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  institution: string;
}

export class AffiliationHighlightDto extends BaseHighlight {
  @ApiProperty({ enum: [HighlightType.AFFILIATION], example: HighlightType.AFFILIATION })
  readonly type = HighlightType.AFFILIATION;

  @ApiProperty({ example: 'Stanford University' })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  institution: string;

  @ApiProperty({ example: 2020 })
  @IsNumber()
  year: number;
}

export class AccomplishmentHighlightDto extends BaseHighlight {
  @ApiProperty({ enum: [HighlightType.ACCOMPLISHMENT], example: HighlightType.ACCOMPLISHMENT })
  readonly type = HighlightType.ACCOMPLISHMENT;

  @ApiProperty({ example: 'Published 15 research papers in top-tier journals' })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  description: string;
}

export type HighlightUnion =
  | AwardHighlightDto
  | AffiliationHighlightDto
  | AccomplishmentHighlightDto;

const HighlightTypeToDto = {
  [HighlightType.AWARD]: AwardHighlightDto,
  [HighlightType.AFFILIATION]: AffiliationHighlightDto,
  [HighlightType.ACCOMPLISHMENT]: AccomplishmentHighlightDto,
};

// ---------- Basic About Section ----------
export class BasicAboutSectionDto {
  @ApiPropertyOptional({
    example: 'Experienced professional...',
    description: 'Professional biography',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  biography?: string;

  @ApiPropertyOptional({ example: '/public-video-url', nullable: true })
  @ValidateIf((_, value) => value !== null)
  @IsOptional()
  @IsString()
  publicVideo?: string | null;

  @ApiPropertyOptional({ example: '/pro-org-video-url', nullable: true })
  @ValidateIf((_, value) => value !== null)
  @IsOptional()
  @IsString()
  professionalOrOrganisationVideo?: string | null;

  @ApiPropertyOptional({
    description: 'Array of highlights',
    type: 'array',
    items: {
      oneOf: [
        { $ref: getSchemaPath(AwardHighlightDto) },
        { $ref: getSchemaPath(AffiliationHighlightDto) },
        { $ref: getSchemaPath(AccomplishmentHighlightDto) },
      ],
    },
  })
  @IsOptional()
  @IsArray()
  @ArrayMaxSize(3, { message: 'Only a maximum of 3 highlight items are allowed.' })
  @ValidateNested({ each: true })
  @Transform(
    ({ value }) => {
      if (!Array.isArray(value)) return value;
      return value.map((item) => {
        const DtoClass = (HighlightTypeToDto as any)[item.type];
        return DtoClass ? Object.assign(new DtoClass(), item) : item;
      });
    },
    { toClassOnly: true },
  )
  highlights?: HighlightUnion[];
}

@ApiExtraModels(
  AwardHighlightDto,
  AffiliationHighlightDto,
  AccomplishmentHighlightDto,
  BasicAboutSectionDto,
  QualificationsSectionDto,
)
export class UpdateAboutDto {
  @ApiPropertyOptional({
    description: 'Basic about section',
    type: BasicAboutSectionDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => BasicAboutSectionDto)
  basic?: BasicAboutSectionDto;

  @ApiPropertyOptional({
    description: 'Qualifications section',
    type: QualificationsSectionDto,
  })
  @ValidateNested()
  @Type(() => QualificationsSectionDto)
  @IsObject()
  @IsOptional()
  [ProfileSection.QUALIFICATION]?: QualificationsSectionDto;

  @ApiPropertyOptional({
    description: `Array of work experiences (max ${MAX_WORK_EXPERIENCE_ITEMS} workplaces)`,
    type: [WorkExperienceDto],
  })
  @ArrayMaxSize(MAX_WORK_EXPERIENCE_ITEMS, {
    message: `Only a maximum of ${MAX_WORK_EXPERIENCE_ITEMS} workplaces are allowed.`,
  })
  @ValidateNested({ each: true })
  @Type(() => WorkExperienceDto)
  @IsArray()
  @IsOptional()
  [ProfileSection.WORK_EXPERIENCE]?: WorkExperienceDto[];

  @ApiPropertyOptional({ description: 'Array of clinical interests', type: [ClinicalInterestDto] })
  @ArrayMaxSize(MAX_CLINICAL_INTEREST_ITEMS)
  @ValidateNested({ each: true })
  @Type(() => ClinicalInterestDto)
  @IsArray()
  @IsOptional()
  [ProfileSection.CLINICAL_INTEREST]?: ClinicalInterestDto[];

  @ApiPropertyOptional({
    description: 'Array of Area of Expertise Items',
    type: [AreaOfExpertiseDto],
  })
  @ArrayMaxSize(MAX_AREA_OF_EXPERTISE_ITEMS)
  @ValidateNested({ each: true })
  @Type(() => AreaOfExpertiseDto)
  @IsArray()
  @IsOptional()
  [ProfileSection.AREA_OF_EXPERTISE]?: AreaOfExpertiseDto[];

  @ApiPropertyOptional({
    description: 'Array of Frequently Asked Questions',
    type: [FAQDto],
  })
  @ArrayMaxSize(MAX_FAQ_ITEMS)
  @ValidateNested({ each: true })
  @Type(() => FAQDto)
  @IsArray()
  @IsOptional()
  [ProfileSection.FAQ]?: FAQDto[];

  @ApiPropertyOptional({
    description: 'Array of Services',
    type: [ServicesDto],
  })
  @ArrayMaxSize(MAX_SERVICES_ITEMS)
  @ValidateNested({ each: true })
  @Type(() => ServicesDto)
  @IsArray()
  @IsOptional()
  [ProfileSection.SERVICE]?: ServicesDto[];

  @ApiPropertyOptional({
    description: 'Get in touch section',
    type: GetInTouchDto,
  })
  @ValidateNested()
  @Type(() => GetInTouchDto)
  @IsObject()
  @IsOptional()
  [ProfileSection.GET_IN_TOUCH]?: GetInTouchDto;

  @ApiPropertyOptional({
    description: 'Array of showcases',
    type: [ShowcaseDto],
  })
  @ArrayMaxSize(MAX_SHOWCASE_ITEMS)
  @ValidateNested({ each: true })
  @Type(() => ShowcaseDto)
  @IsArray()
  @IsOptional()
  [ProfileSection.SHOWCASE]?: ShowcaseDto[];

  @ApiPropertyOptional({
    description: 'Fundraiser section',
    type: FundraiserSectionDto,
  })
  @ValidateNested()
  @Type(() => FundraiserSectionDto)
  @IsObject()
  @IsOptional()
  [ProfileSection.FUNDRAISER]?: FundraiserSectionDto;

  @ApiPropertyOptional({
    description: 'Custom section',
    type: CustomSectionDto,
  })
  @ValidateNested()
  @Type(() => CustomSectionDto)
  @IsObject()
  @IsOptional()
  [ProfileSection.CUSTOM]?: CustomSectionDto;
}
