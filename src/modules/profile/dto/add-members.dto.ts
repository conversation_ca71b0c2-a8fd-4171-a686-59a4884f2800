import {
  IsString,
  IsArray,
  ArrayUnique,
  ValidateNested,
  IsObject,
  IsBoolean,
  IsUUID,
  IsNotEmpty,
  IsEmail,
  ValidateIf,
} from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class NewUserDto {
  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'connect the newly created users while they join',
    type: 'boolean',
    example: true,
    required: true,
  })
  @IsBoolean()
  autoConnectOnAccept: boolean;

  @ApiProperty({
    name: 'name',
    type: 'string',
    required: false,
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @ValidateIf((o) => o.name !== undefined)
  name?: string;

  @ApiProperty({
    name: 'role',
    type: 'string',
    required: false,
    example: 'Sr Surgeon',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @ValidateIf((o) => o.role !== undefined)
  role?: string;
}

export class TeamMemberManageDto {
  @ApiProperty({
    name: 'workspaceIds',
    type: [String],
    required: true,
    isArray: true,
    example: ['1d1b818e-bd17-44b1-98f8-62d4436c8be3', '208e504c-f7ab-449e-9989-28e703ff6808'],
  })
  @IsUUID()
  @ArrayUnique()
  @IsArray()
  workspaceIds: string[];

  @ApiProperty({
    name: 'newUsers',
    description: 'new users to be invited',
    type: [NewUserDto],
    example: [
      {
        email: '<EMAIL>',
        name: 'John',
        role: 'Sr Surgeon',
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NewUserDto)
  @IsObject()
  newUsers: NewUserDto[];
}
