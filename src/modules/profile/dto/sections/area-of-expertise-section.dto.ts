import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

import { MAX_PROFILE_TEXT_LENGTH, MIN_PROFILE_TEXT_LENGTH } from '@/constants/profile';

export class AreaOfExpertiseDto {
  @ApiProperty({ example: 'Echocardiography' })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiPropertyOptional({
    example:
      'A comprehensive heart health evaluation tailored to your medical history, symptoms, and risk factors. During your consultation, I assess cardiovascular risks such as hypertension, high cholesterol, and family history of heart disease. If needed, diagnostic tests are recommended to develop a personalized prevention or treatment plan.',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsOptional()
  description?: string;
}
