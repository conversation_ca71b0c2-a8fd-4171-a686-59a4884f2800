import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsNotEmpty, IsString, MaxLength, MinLength } from 'class-validator';

import { MAX_PROFILE_TEXT_LENGTH, MIN_PROFILE_TEXT_LENGTH } from '@/constants/profile';

export class FAQDto {
  @ApiProperty({ example: 'What can I expect during a consultation?' })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty({
    example:
      'During your consultation, I will review your medical history, current symptoms, and any previous test results. If necessary, we may perform diagnostic tests such as an ECG, echocardiogram, or stress test. Based on the findings, I will discuss your diagnosis, treatment options, and next steps to manage your heart health.',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  answer: string;
}
