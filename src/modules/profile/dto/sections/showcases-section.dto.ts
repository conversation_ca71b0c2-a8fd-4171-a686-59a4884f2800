import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsString, MaxLength, MinLength } from 'class-validator';

import { MAX_SHOWCASE_TEXT_LENGTH, MIN_SHOWCASE_TEXT_LENGTH } from '@/constants/profile';
import { MEDIA_CATEGORY } from '@/constants/posts';

// ✅ Only pick allowed values
export const ALLOWED_MEDIA = [MEDIA_CATEGORY.IMAGE, MEDIA_CATEGORY.VIDEO] as const;
export type AllowedMediaType = (typeof ALLOWED_MEDIA)[number]; // 'image' | 'video'

export class ShowcaseDto {
  @ApiProperty({ example: '/path/image.jpg' })
  @IsString()
  @IsNotEmpty()
  mediaUrl: string;

  @ApiProperty({ example: MEDIA_CATEGORY.IMAGE, enum: ALLOWED_MEDIA })
  @IsEnum(ALLOWED_MEDIA, { message: `mediaType must be one of: ${ALLOWED_MEDIA.join(', ')}` })
  mediaType: AllowedMediaType;

  @ApiProperty({ example: 'Echocardiography Scan' })
  @MaxLength(MAX_SHOWCASE_TEXT_LENGTH)
  @MinLength(MIN_SHOWCASE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    example: 'Sample echocardiography image highlighting heart chambers',
  })
  @MaxLength(MAX_SHOWCASE_TEXT_LENGTH)
  @MinLength(MIN_SHOWCASE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  description: string;
}
