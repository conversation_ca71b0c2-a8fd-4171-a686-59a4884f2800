import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON><PERSON>th,
  <PERSON>idateNested,
} from 'class-validator';

import {
  MAX_CONTACT_TEXT_LENGTH,
  MIN_CONTACT_TEXT_LENGTH,
  MAX_CONTACT_ITEMS,
  MAX_PHONE_LENGTH,
  MIN_PHONE_LENGTH,
} from '@/constants/profile';

export class ContactPhoneDto {
  @ApiPropertyOptional({ example: 'Support' })
  @MaxLength(MAX_CONTACT_TEXT_LENGTH)
  @MinLength(MIN_CONTACT_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ example: '+1234567890' })
  @Matches(/^\+?[0-9]+$/, {
    message: 'Phone number can only contain numbers and an optional + at the beginning',
  })
  @MaxLength(MAX_PHONE_LENGTH)
  @MinLength(MIN_PHONE_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;
}

export class ContactEmailDto {
  @ApiPropertyOptional({ example: 'General Inquiries' })
  @MaxLength(MAX_CONTACT_TEXT_LENGTH)
  @MinLength(MIN_CONTACT_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ example: '<EMAIL>' })
  @MaxLength(MAX_CONTACT_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class ContactAddressDto {
  @ApiProperty({ example: '123 Main Street' })
  @MaxLength(MAX_CONTACT_TEXT_LENGTH)
  @MinLength(MIN_CONTACT_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @ApiProperty({ example: 'Suite 100' })
  @MaxLength(MAX_CONTACT_TEXT_LENGTH)
  @MinLength(MIN_CONTACT_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  addressLine2: string;

  @ApiProperty({ example: 'New York' })
  @MaxLength(MAX_CONTACT_TEXT_LENGTH)
  @MinLength(MIN_CONTACT_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({ example: '10001' })
  @MaxLength(20)
  @MinLength(3)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  postcode: string;

  @ApiProperty({ example: 'United States' })
  @MaxLength(MAX_CONTACT_TEXT_LENGTH)
  @MinLength(MIN_CONTACT_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  country: string;
}

export class GetInTouchDto {
  @ApiProperty({
    description: 'Array of contact phone numbers',
    type: [ContactPhoneDto],
  })
  @ValidateNested({ each: true })
  @Type(() => ContactPhoneDto)
  @ArrayMaxSize(MAX_CONTACT_ITEMS)
  @IsArray()
  phones: ContactPhoneDto[];

  @ApiProperty({
    description: 'Array of contact email addresses',
    type: [ContactEmailDto],
  })
  @ValidateNested({ each: true })
  @Type(() => ContactEmailDto)
  @ArrayMaxSize(MAX_CONTACT_ITEMS)
  @IsArray()
  emails: ContactEmailDto[];

  @ApiProperty({
    description: 'Contact address information',
    type: ContactAddressDto,
  })
  @ValidateNested()
  @Type(() => ContactAddressDto)
  address: ContactAddressDto;
}
