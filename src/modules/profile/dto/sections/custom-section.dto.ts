import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsHexColor,
  IsNotEmpty,
  IsObject,
  IsString,
  MaxLength,
  Min<PERSON>ength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import {
  ALLOWED_MEDIA,
  AllowedMediaType,
  CustomSectionLayout,
  CustomSectionType,
  MAX_PROFILE_TEXT_LENGTH,
  MediaShape,
  MIN_PROFILE_TEXT_LENGTH,
} from '@/constants/profile';

export class CustomStyleOptionsDto {
  @ApiProperty({
    description: 'Media shape (square or circle)',
    enum: MediaShape,
    example: MediaShape.SQUARE,
  })
  @IsEnum(MediaShape)
  mediaShape: MediaShape;

  @ApiProperty({
    description: 'Enable tint background',
    example: true,
  })
  @IsBoolean()
  tintBgEnabled: boolean;

  @ApiProperty({
    description: 'Tint background color in hex',
    example: '#FF5733',
  })
  @IsHexColor()
  tintBgColor: string;
}

export class CustomSectionDto {
  @ApiProperty({
    description: 'Custom section type (text or media)',
    enum: CustomSectionType,
    example: CustomSectionType.MEDIA,
  })
  @IsEnum(CustomSectionType)
  sectionType: CustomSectionType;

  @ApiPropertyOptional({
    description: 'Custom section layout type, required if sectionType is media',
    enum: CustomSectionLayout,
    example: CustomSectionLayout.LEFT_ALIGNED_IMAGE,
  })
  @Transform(({ obj, value }) => {
    // Remove field if section type is not media
    if (obj.sectionType !== CustomSectionType.MEDIA) {
      return undefined;
    }
    return value;
  })
  @ValidateIf((o) => o.sectionType === CustomSectionType.MEDIA)
  @IsEnum(CustomSectionLayout)
  layout: CustomSectionLayout | undefined;

  @ApiProperty({
    description: 'Media path - only required if sectionType is media',
    example: '/smiling-kid.jpg',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => o.sectionType === CustomSectionType.MEDIA)
  @Transform(({ obj, value }) => {
    // Remove field if section type is not media
    if (obj.sectionType !== CustomSectionType.MEDIA) {
      return undefined;
    }
    return value?.trim();
  })
  mediaUrl: string | undefined;

  @ApiProperty({
    description: 'Media type - only required if sectionType is media',
    example: ALLOWED_MEDIA[0],
    enum: ALLOWED_MEDIA,
  })
  @IsEnum(ALLOWED_MEDIA, { message: `mediaType must be one of: ${ALLOWED_MEDIA.join(', ')}` })
  @ValidateIf((o) => o.sectionType === CustomSectionType.MEDIA)
  @Transform(({ obj, value }) => {
    // Remove field if section type is not media
    if (obj.sectionType !== CustomSectionType.MEDIA) {
      return undefined;
    }
    return value?.trim();
  })
  mediaType: AllowedMediaType | undefined;

  @ApiProperty({
    description: 'custom section title',
    example: 'Who am i',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  title: string;

  @ApiProperty({
    description: 'custom section description',
    example: 'I am a cardiologist',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  description: string;

  @ApiProperty({
    description: 'format media options for media',
    type: CustomStyleOptionsDto,
    example: {
      mediaShape: MediaShape.SQUARE,
      tintBgEnabled: true,
      tintBgColor: '#FF5733',
    },
  })
  @ValidateNested()
  @Type(() => CustomStyleOptionsDto)
  @IsObject()
  @ValidateIf((o) => o.sectionType === CustomSectionType.MEDIA)
  @Transform(({ obj, value }) => {
    // Remove field if section type is not media
    if (obj.sectionType !== CustomSectionType.MEDIA) {
      return undefined;
    }
    return value;
  })
  formatMedia: CustomStyleOptionsDto | undefined;
}
