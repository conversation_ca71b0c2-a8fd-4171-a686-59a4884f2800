import {
  IsString,
  IsArray,
  ValidateNested,
  IsNotEmpty,
  IsNumber,
  ArrayMaxSize,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Is<PERSON>ptional,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  Validate,
} from 'class-validator';
import { Type, Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import {
  MAX_PROFILE_TEXT_LENGTH,
  MAX_WORK_EXPERIENCE_ROLES_ITEMS,
  MIN_PROFILE_TEXT_LENGTH,
} from '@/constants/profile';

import { endYearMustBeGreaterThanStartYear } from '@/exceptions/profile';

// --- Custom validator ---
@ValidatorConstraint({ name: 'ToYearAfterFromYear', async: false })
class ToYearAfterFromYearConstraint implements ValidatorConstraintInterface {
  validate(toYear: number, args: ValidationArguments) {
    const obj = args.object as any;
    if (toYear === undefined || toYear === null) return true; // skip if not provided
    return toYear >= obj.fromYear;
  }
  defaultMessage() {
    return endYearMustBeGreaterThanStartYear().message;
  }
}

// Role DTO (nested within work experience)
export class RoleDto {
  @ApiProperty({
    example: 'Senior Software Engineer',
    description: 'Job title or role',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  role: string;

  @ApiProperty({
    example: 2020,
    description: 'Year when this role started',
  })
  @Max(new Date().getFullYear())
  @Min(1950) // Reasonable minimum for work history
  @IsNumber()
  @IsNotEmpty()
  fromYear: number;

  @ApiPropertyOptional({
    example: 2023,
    description: 'Year when this role ended',
    nullable: true,
    required: false,
  })
  @Validate(ToYearAfterFromYearConstraint)
  @Max(new Date().getFullYear())
  @Min(1950)
  @IsNumber()
  @IsOptional()
  toYear?: number | null;
}

// Work Experience DTO
export class WorkExperienceDto {
  @ApiProperty({
    example: 'Google Inc.',
    description: 'Name of the workplace/company/organization',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  workplace: string;

  @ApiProperty({
    description: `Array of roles at this workplace (max ${MAX_WORK_EXPERIENCE_ROLES_ITEMS} roles per workplace)`,
    type: [RoleDto],
    examples: [
      {
        role: 'Software Engineer',
        fromYear: 2018,
        toYear: 2020,
      },
      {
        role: 'Senior Software Engineer',
        fromYear: 2020,
        toYear: 2023,
      },
      {
        role: 'Project Manager',
        fromYear: 2023,
      },
    ],
  })
  @IsArray()
  @ArrayMaxSize(MAX_WORK_EXPERIENCE_ROLES_ITEMS, {
    message: `Only a maximum of ${MAX_WORK_EXPERIENCE_ROLES_ITEMS} roles are allowed per workplace.`,
  })
  @ValidateNested({ each: true })
  @Type(() => RoleDto)
  roles: RoleDto[];
}
