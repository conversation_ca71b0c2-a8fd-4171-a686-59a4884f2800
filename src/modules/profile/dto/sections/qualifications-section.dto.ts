import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Nested,
} from 'class-validator';

import {
  MAX_PROFILE_TEXT_LENGTH,
  MAX_QUALIFICATION_ITEMS,
  MIN_PROFILE_TEXT_LENGTH,
} from '@/constants/profile';

export class DegreeDto {
  @ApiProperty({ example: 'Bachelor of Engineering' })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  degree: string;

  @ApiPropertyOptional({ example: 'Computer Science' })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsOptional()
  specialisation?: string;

  @ApiProperty({ example: 2020 })
  @Max(new Date().getFullYear() + 10)
  @Min(1900)
  @IsNumber()
  yearOfAward: number;

  @ApiProperty({ example: 'MIT' })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  institution: string;
}

export class RegistrationAndLicenseDto {
  @ApiProperty({ example: 'State Medical Board' })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  registeredWith: string;

  @ApiProperty({ example: 2019 })
  @Min(1900)
  @Max(new Date().getFullYear() + 10)
  @IsNumber()
  @IsNotEmpty()
  yearOfRegistration: number;

  @ApiProperty({ example: 'License #MED67890' })
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  details: string;
}

export class QualificationsSectionDto {
  @ApiProperty({ description: 'Array of academic degrees', type: [DegreeDto] })
  @ValidateNested({ each: true })
  @Type(() => DegreeDto)
  @ArrayMaxSize(MAX_QUALIFICATION_ITEMS)
  @IsArray()
  @IsNotEmpty()
  academicDegrees: DegreeDto[];

  @ApiProperty({ description: 'Array of professional degrees', type: [DegreeDto] })
  @ValidateNested({ each: true })
  @Type(() => DegreeDto)
  @ArrayMaxSize(MAX_QUALIFICATION_ITEMS)
  @IsArray()
  @IsNotEmpty()
  professionalDegrees: DegreeDto[];

  @ApiProperty({
    description: 'Array of registrations/licenses',
    type: [RegistrationAndLicenseDto],
  })
  @ValidateNested({ each: true })
  @Type(() => RegistrationAndLicenseDto)
  @ArrayMaxSize(MAX_QUALIFICATION_ITEMS)
  @IsArray()
  @IsNotEmpty()
  registrationAndLicenses: RegistrationAndLicenseDto[];
}
