import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsHexColor,
  IsNotEmpty,
  IsObject,
  IsString,
  IsUrl,
  <PERSON>Length,
  MinLength,
  Validate,
  ValidateIf,
  ValidateNested,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

import { IsOptionalNonNullable } from '@/decorators/is-optional-not-nullable';

import {
  DonationMethod,
  FundraiserLayout,
  MAX_PROFILE_TEXT_LENGTH,
  MIN_PROFILE_TEXT_LENGTH,
} from '@/constants/profile';

// Custom validator for style options based on layout
@ValidatorConstraint({ name: 'styleOptionsLayoutValidator', async: false })
export class StyleOptionsLayoutValidator implements ValidatorConstraintInterface {
  private errMessage = '';

  validate(styleOptions: FundraiserStyleOptionsDto, args: ValidationArguments): boolean {
    const parent = args.object as FundraiserSectionDto;
    const layout = parent.layout;

    if (!styleOptions || !layout) {
      return true; // skip if missing
    }

    return this.validateStyleOptionsForLayout(styleOptions, layout);
  }

  private validateStyleOptionsForLayout(
    styleOptions: FundraiserStyleOptionsDto,
    layout: FundraiserLayout,
  ): boolean {
    // Fields always allowed for any layout
    type StyleOptionKey = keyof FundraiserStyleOptionsDto;

    const permittedFields: StyleOptionKey[] = ['textColor']; // always allowed

    switch (layout) {
      case FundraiserLayout.BG_CENTER:
        permittedFields.push('blurBg', 'tintBgEnabled', 'tintBgColor');
        break;

      case FundraiserLayout.SIDE_IMAGE:
        permittedFields.push('tintBgEnabled', 'tintBgColor');
        break;

      case FundraiserLayout.BG_LEFT_ALIGNED:
        permittedFields.push('gradientColor');
        break;
    }

    // 1. Ensure all permitted fields are defined (not undefined)
    for (const fieldName of permittedFields) {
      if (styleOptions[fieldName] === undefined) {
        return this.fail(`${fieldName} is required for ${layout} layout`);
      }
    }

    // 2. Ensure no unsupported fields are provided (early exit on first invalid)
    for (const [fieldName, value] of Object.entries(styleOptions)) {
      if (value !== undefined && !permittedFields.includes(fieldName as StyleOptionKey)) {
        return this.fail(`Invalid field for ${layout} layout: ${fieldName}`);
      }
    }

    return true;
  }

  private fail(message: string): false {
    this.errMessage = message;
    return false;
  }

  defaultMessage(): string {
    return this.errMessage;
  }
}

// Simplified StyleOptions DTO - remove all the ValidateIf and Transform logic
// Keep only basic format validation
export class FundraiserStyleOptionsDto {
  @ApiPropertyOptional({
    description: 'Apply blur effect (only for bgCenter layout)',
    example: true,
  })
  @IsOptionalNonNullable()
  @IsBoolean()
  blurBg?: boolean;

  @ApiPropertyOptional({
    description: 'Enable tint background (only for bgCenter or sideImage)',
    example: true,
  })
  @IsOptionalNonNullable()
  @IsBoolean()
  tintBgEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Tint background color in hex (only for bgCenter or sideImage)',
    example: '#FF5733',
  })
  @IsOptionalNonNullable()
  @IsHexColor()
  tintBgColor?: string;

  @ApiPropertyOptional({
    description: 'Gradient color in hex (only for bgLeft_aligned)',
    example: '#00BFFF',
  })
  @IsOptionalNonNullable()
  @IsHexColor()
  gradientColor?: string;

  @ApiProperty({
    description: 'Text color in hex',
    example: '#FFFFFF',
  })
  @IsHexColor()
  @IsNotEmpty()
  textColor: string;
}

export class FundraiserSectionDto {
  @ApiProperty({
    description: 'Fundraiser layout type',
    enum: FundraiserLayout,
    example: FundraiserLayout.BG_CENTER,
  })
  @IsEnum(FundraiserLayout)
  layout: FundraiserLayout;

  @ApiProperty({
    description: 'Background or side image URL',
    example: 'https://cdn.example.com/fundraiser/hero-image.jpg',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  backgroundImageUrl: string;

  @ApiProperty({
    description: 'Fundraiser description',
    example:
      'Your support helps us provide life-saving cardiac care to underprivileged communities.',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'CTA button text (e.g. Donate Now)',
    example: 'Donate Now',
  })
  @MaxLength(MAX_PROFILE_TEXT_LENGTH)
  @MinLength(MIN_PROFILE_TEXT_LENGTH)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  ctaText: string;

  @ApiProperty({
    description: 'Styling options depending on layout',
    type: FundraiserStyleOptionsDto,
    example: {
      blurBg: true,
      tintBgEnabled: true,
      tintBgColor: '#FF5733',
      textColor: '#FFFFFF',
    },
  })
  @ValidateNested()
  @Type(() => FundraiserStyleOptionsDto)
  @Validate(StyleOptionsLayoutValidator) // Custom validator with access to layout
  @IsObject()
  styleOptions: FundraiserStyleOptionsDto;

  @ApiProperty({
    description: 'Donation method (minicardiacService or donationLink)',
    enum: DonationMethod,
    example: DonationMethod.DONATION_LINK,
  })
  @IsEnum(DonationMethod)
  donationMethod: DonationMethod;

  @ApiPropertyOptional({
    description: 'Redirect URL (required if donationMethod = donationLink)',
    example: 'https://donate.example.com/campaign/123',
  })
  @Transform(({ obj, value }) => {
    // Remove field if donationMethod is not DONATION_LINK
    if (obj.donationMethod !== DonationMethod.DONATION_LINK) {
      return undefined;
    }
    return value;
  })
  @ValidateIf((o) => o.donationMethod === DonationMethod.DONATION_LINK)
  @IsUrl({ protocols: ['https'] })
  donationLink?: string;
}
