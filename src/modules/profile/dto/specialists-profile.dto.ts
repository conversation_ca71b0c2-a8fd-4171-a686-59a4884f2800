import { IsInt, IsOptional, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { BaseProfileDto } from './base-profile.dto';

// Specifically for surgeon, cardiologist & allied cardiac
export class SpecialistsProfileDto extends BaseProfileDto {
  @ApiProperty({
    name: 'title',
    type: 'string',
    required: false,
    example: 'Dr.',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(1)
  title?: string;

  @ApiProperty({
    name: 'qualifications',
    type: 'string',
    required: false,
    example: 'MD, PhD',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(1)
  qualifications?: string;

  @ApiProperty({
    name: 'jobTitle',
    type: 'string',
    required: false,
    example: 'Head of Cardiology',
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(1)
  jobTitle?: string;

  @ApiProperty({
    name: 'employerId',
    type: 'string',
    required: false,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  employerId?: string;

  @ApiProperty({
    name: 'jobTitleYear',
    type: 'integer',
    required: false,
    example: 2022,
    description: 'Year in 4-digit format, e.g., 2022',
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsInt({ message: 'Year must be a valid integer' })
  @Min(1900, { message: 'Year cannot be before 1900' })
  @Max(new Date().getFullYear(), {
    message: `Year cannot be after the current year`,
  })
  jobTitleYear?: number;
}
