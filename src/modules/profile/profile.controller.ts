import {
  Controller,
  Get,
  Param,
  Patch,
  Body,
  Inject,
  ParseUUIDPipe,
  Post,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiTags, getSchemaPath } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { and, eq, gte } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { BaseProfileDto } from './dto/base-profile.dto';
import { UpdateAboutDto } from './dto/update-about-section.dto';
import { StudentBasicProfileDto } from './dto/student-profile.dto';
import { SpecialistsProfileDto } from './dto/specialists-profile.dto';
import { OrganisationProfileDto } from './dto/organisation-profile.dto';
import { NewReviewDto } from './reviews/dto/new-review.dto';
import { FetchReviewsQueryDto } from './reviews/dto/get-reviews.dto';
import { UpdateReviewEnableDisableStatusDto } from './reviews/dto/update-enable-disable-status.dto';
import { GetMembersSuggessionDto } from './dto/get-members-suggession.dto';
import { TeamMemberManageDto } from './dto/add-members.dto';

import * as schema from '@/db/schema';
import {
  workspaces,
  users,
  professionals,
  specialists,
  students,
  organisations,
} from '@/db/schema';

export interface SuggestionParams {
  workspaceId: string;
  excludeIds?: string[];
  searchKeyword?: string;
  limit: number;
  offset: number;
}

export interface UserSuggestion {
  id: string;
  displayName: string;
  username: string;
  email: string;
  profileImageUrlThumbnail: string | null;
  priority: number; // 1 = highest priority
  reason: 'connection' | 'follower' | 'same_organization' | 'other';
}

import { User } from '@/decorators/user.decorator';
import { AccountSetupStages } from '@/decorators/account-setup-stage.decorator';

import { Role } from '@/constants/roles';
import { AuthConstants } from '@/constants/auth';
import { EntityType } from '@/constants/user-types';
import { WorkspacesStatus } from '@/constants/workspaces';
import { AccountSetupStage, UserStatus } from '@/constants/users';
import { EntityName } from '@/constants/entities';
import { UserSegment } from '@/constants/user-segments';
import { ProfileSection } from '@/constants/profile';

import * as systemExceptions from '@/exceptions/system';
import { itemNotFound } from '@/exceptions/common';
import { reviewsDisabledCreate, reviewsDisabledView } from '@/exceptions/profile';
import { restrictedSectionFieldsBasedOnAccountOrSubscription } from '@/exceptions/workspaces';

import { ProfileService } from './profile.service';
import { WorkspacesService } from '@/modules/workspaces/workspaces.service';

import { removeUndefinedValues } from '@/utils/object';

import { Public } from '@/decorators/public.decorator';

type ProfileDtoUnion =
  | SpecialistsProfileDto
  | StudentBasicProfileDto
  | OrganisationProfileDto
  | BaseProfileDto;

@ApiExtraModels(
  SpecialistsProfileDto,
  StudentBasicProfileDto,
  OrganisationProfileDto,
  BaseProfileDto,
)
@Controller('profile')
@ApiTags('profile')
@ApiBearerAuth()
export class ProfileController {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly profileService: ProfileService,
    private readonly workspaceService: WorkspacesService,
  ) {}

  @Get('user/section')
  async getUserSection(@User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string) {
    if (!workspaceId) {
      throw itemNotFound(EntityName.WORKSPACE);
    }

    const { segment, isPremium } =
      await this.profileService.getUserSegmentAndSubscriptionStatus(workspaceId);

    return this.profileService.getSectionDetails(segment, isPremium);
  }

  @Get('about')
  async findOwnAbout(@User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string) {
    return this.profileService.findAboutById(workspaceId);
  }

  @Get(':username')
  async findProfileByUsername(
    @Param('username') username: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.profileService.findByUsername(
      username,
      workspaceId ?? userId,
      workspaceId ? EntityType.WORKSPACE : EntityType.USER,
    );
  }

  @Get()
  async findOwnProfile(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.profileService.findById(
      userId,
      workspaceId ?? userId,
      workspaceId ? EntityType.WORKSPACE : EntityType.USER,
    );
  }

  @ApiBody({
    schema: {
      oneOf: [
        { $ref: getSchemaPath(SpecialistsProfileDto) },
        { $ref: getSchemaPath(StudentBasicProfileDto) },
        { $ref: getSchemaPath(OrganisationProfileDto) },
        { $ref: getSchemaPath(BaseProfileDto) },
      ],
    },
    description: 'Accepts different DTOs based on the user role',
  })
  @Patch()
  @AccountSetupStages(AccountSetupStage.COMPLETED)
  async updateBasicInformation(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_ROLE) role: Role,
    @Body()
    body: ProfileDtoUnion,
  ) {
    const dtoMap: Partial<Record<Role, any>> = {
      [Role.CARDIAC_SPECIALIST]: SpecialistsProfileDto,
      [Role.ALLIED_CARDIAC]: SpecialistsProfileDto,
      [Role.STUDENT]: StudentBasicProfileDto,
      [Role.ORGANISATION]: OrganisationProfileDto,
      [Role.PUBLIC]: BaseProfileDto,
    };

    const SelectedDto = dtoMap[role] ?? BaseProfileDto;

    const dtoInstance: ProfileDtoUnion = plainToInstance(SelectedDto, body);
    const errors = await validate(dtoInstance, {
      whitelist: true,
      forbidNonWhitelisted: true,
    });

    if (errors.length > 0) {
      throw systemExceptions.validationError(undefined, {
        metadata: errors,
      });
    }

    await this.drizzleDev.transaction(async (txn) => {
      await this.profileService.updateBaseProfile(userId, dtoInstance, txn);

      const roleToServiceMethod: Partial<
        Record<
          Role,
          {
            updateMethod: (
              workspaceId: string,
              dtoInstance: ProfileDtoUnion,
              txn: any,
            ) => Promise<void>;
            table:
              | typeof professionals
              | typeof specialists
              | typeof students
              | typeof organisations;
          }
        >
      > = {
        [Role.ALLIED_CARDIAC]: {
          updateMethod: this.profileService.updateAlliedCardiacBaseProfile.bind(
            this.profileService,
          ),
          table: professionals,
        },
        [Role.CARDIAC_SPECIALIST]: {
          updateMethod: this.profileService.updateSpecialistsBaseProfile.bind(this.profileService),
          table: specialists,
        },
        [Role.STUDENT]: {
          updateMethod: this.profileService.updateStudentBasicProfile.bind(this.profileService),
          table: students,
        },
        [Role.ORGANISATION]: {
          updateMethod: this.profileService.updateOrganisationBasicProfile.bind(
            this.profileService,
          ),
          table: organisations,
        },
      };

      const mapping = roleToServiceMethod[role];

      if (mapping) {
        if (!workspaceId) throw systemExceptions.unauthorized();

        const { updateMethod, table } = mapping;

        const relevantFields = Object.keys(dtoInstance).filter(
          (key) =>
            Object.keys(table).includes(key) &&
            dtoInstance[key as keyof typeof dtoInstance] !== undefined,
        );

        if (relevantFields.length > 0) await updateMethod(workspaceId, dtoInstance, txn);
      }
    });

    return true;
  }

  @Patch('about')
  async updateAboutSection(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() body: UpdateAboutDto,
  ) {
    const { basic, ...restSections } = body;

    // If workspaceId is present, retrieve the workspace owner's userId
    const effectiveUserId = await this.getEffectiveUserId(userId, workspaceId);

    if (!effectiveUserId) throw systemExceptions.unauthorized();

    await this.drizzleDev.transaction(async (txn) => {
      if (basic) {
        await this.profileService.handleBasicSectionUpdate(
          effectiveUserId,
          workspaceId,
          basic,
          txn,
        );
      }

      const restSectionOnlyValid = removeUndefinedValues(restSections);

      if (Object.keys(restSectionOnlyValid).length > 0) {
        const { segment, isPremium } =
          await this.profileService.getUserSegmentAndSubscriptionStatus(workspaceId);

        const isSectionsAllowed = this.profileService.isSectionsAllowed(
          segment,
          isPremium,
          Object.keys(restSectionOnlyValid) as ProfileSection[],
        );

        if (!isSectionsAllowed) throw restrictedSectionFieldsBasedOnAccountOrSubscription();

        const {
          qualifications,
          workExperiences,
          clinicalInterests,
          areaOfExpertises,
          faqs,
          services,
          getInTouch,
          showcases,
          fundraiser,
          custom,
        } = restSections;

        if (qualifications) {
          await this.profileService.handleQualificationSection(
            workspaceId,
            qualifications,
            segment as
              | UserSegment.ALLIED_CARDIAC
              | UserSegment.CARDIAC_SPECIALIST
              | UserSegment.STUDENT,
            txn,
          );
        }

        if (workExperiences) {
          await this.profileService.handleWorkExperienceSection(
            workspaceId,
            workExperiences,
            segment as
              | UserSegment.ALLIED_CARDIAC
              | UserSegment.CARDIAC_SPECIALIST
              | UserSegment.STUDENT,
            txn,
          );
        }

        // Add other section updates here
        if (clinicalInterests) {
          await this.profileService.handleClinicalInterestSection(
            workspaceId,
            clinicalInterests,
            segment as
              | UserSegment.ALLIED_CARDIAC
              | UserSegment.CARDIAC_SPECIALIST
              | UserSegment.STUDENT,
            txn,
          );
        }

        if (areaOfExpertises) {
          await this.profileService.handleAreaOfExpertiseSection(
            workspaceId,
            areaOfExpertises,
            segment as
              | UserSegment.ALLIED_CARDIAC
              | UserSegment.CARDIAC_SPECIALIST
              | UserSegment.STUDENT,
            txn,
          );
        }

        if (faqs) {
          await this.profileService.handleFAQSection(
            workspaceId,
            faqs,
            segment as
              | UserSegment.ALLIED_CARDIAC
              | UserSegment.CARDIAC_SPECIALIST
              | UserSegment.ORGANISATION,
            txn,
          );
        }

        if (services) {
          await this.profileService.handleServicesSection(workspaceId, services, txn);
        }

        if (getInTouch) {
          await this.profileService.handleGetInTouchSection(
            workspaceId,
            getInTouch,
            segment as
              | UserSegment.ALLIED_CARDIAC
              | UserSegment.CARDIAC_SPECIALIST
              | UserSegment.ORGANISATION,
            txn,
          );
        }

        if (showcases) {
          await this.profileService.handleShowcaseSection(
            workspaceId,
            showcases,
            segment as
              | UserSegment.ALLIED_CARDIAC
              | UserSegment.CARDIAC_SPECIALIST
              | UserSegment.ORGANISATION,
            txn,
          );
        }

        if (fundraiser) {
          await this.profileService.handleFundraiserSection(
            workspaceId,
            fundraiser,
            segment as
              | UserSegment.ALLIED_CARDIAC
              | UserSegment.CARDIAC_SPECIALIST
              | UserSegment.ORGANISATION,
            txn,
          );
        }

        if (custom) {
          await this.profileService.handleCustomSection(workspaceId, custom, txn);
        }
      }
    });

    return true;
  }

  @Get('team/suggessions')
  @Public()
  async getSuggessionUsersss(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string = '208e504c-f7ab-449e-9989-28e703ff6808',
    @Query() query: GetMembersSuggessionDto,
  ) {
    const { excludeIds, searchKeyword, limit, offset } = query;

    const suggestions = await this.profileService.getWorkspaceMemberSuggestions({
      workspaceId,
      excludeIds,
      searchKeyword,
      limit,
      offset,
    });

    return suggestions;
  }

  @Post('team/add')
  async manageTeacMembers(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string = '208e504c-f7ab-449e-9989-28e703ff6808',
    @Body() body: TeamMemberManageDto,
  ) {
    const { newUsers, workspaceIds } = body;

    const suggestions = await this.profileService.manageTeamMembers({
      workspaceId,
      workspaceIds,
      newUsers,
      // autoConnectOnAccept,
    });

    return suggestions;
  }

  private getEffectiveUserId(userId: string, workspaceId: string) {
    return workspaceId
      ? this.drizzleDev
          .select({ userId: workspaces.createdById })
          .from(workspaces)
          .innerJoin(users, eq(users.id, workspaces.createdById))
          .where(
            and(
              eq(workspaces.id, workspaceId),
              gte(workspaces.status, WorkspacesStatus.PENDING), // Allow PENDING as approval flow is not yet implemented
              eq(users.status, UserStatus.ACTIVE),
            ),
          )
          .limit(1)
          .then((res) => res[0]?.userId)
      : userId;
  }

  @Post('workspaces/:workspaceId/reviews')
  async createReview(
    @Body() reviewBody: NewReviewDto,
    @Param('workspaceId', new ParseUUIDPipe({ version: '4' })) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) userWorkspaceId: string,
  ) {
    const workspace = await this.workspaceService.findOneWorkspaceByWorkspaceId(workspaceId);

    // TODO: Please update here while there is option to approve a workspace by the admin
    if (workspace.status < WorkspacesStatus.PENDING) {
      throw itemNotFound(EntityName.WORKSPACE);
    }

    if (!workspace.isRatingAndReviewEnabled) {
      throw reviewsDisabledCreate();
    }

    return this.profileService.createReview(
      reviewBody,
      workspaceId,
      userWorkspaceId ?? userId,
      userWorkspaceId ? EntityType.WORKSPACE : EntityType.USER,
    );
  }

  @Get('workspaces/:workspaceId/reviews')
  async getReviews(
    @Query() query: FetchReviewsQueryDto,
    @Param('workspaceId', new ParseUUIDPipe({ version: '4' })) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) userWorkspaceId: string,
  ) {
    const workspace = await this.workspaceService.findOneWorkspaceByWorkspaceId(workspaceId, true);

    // TODO: Please update here while there is option to approve a workspace by the admin
    if (workspace.status < WorkspacesStatus.PENDING) {
      throw itemNotFound(EntityName.WORKSPACE);
    }

    if (!workspace.isRatingAndReviewEnabled && userWorkspaceId !== workspaceId) {
      throw reviewsDisabledView();
    }

    const reviews = await this.profileService.getReviews({
      workspaceId,
      ...query,
    });

    // If the user is on the first page (offset = 1),
    // also return the average rating and total review count along with the reviews.
    if (query.offset === 1) {
      const averageReviewsCount = await this.profileService.getAverageReview(workspaceId);

      return {
        averageReviewsCount,
        reviews,
      };
    }

    return {
      reviews,
    };
  }

  @Patch('workspaces/reviews/settings')
  async updateReviewEnableStatus(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() dto: UpdateReviewEnableDisableStatusDto,
  ) {
    const updated = await this.workspaceService.updateWorkspace(workspaceId, {
      isRatingAndReviewEnabled: dto.isEnabled,
    });

    if (!updated) throw itemNotFound(EntityName.WORKSPACE);

    return { isRatingAndReviewEnabled: updated.isRatingAndReviewEnabled };
  }
}
