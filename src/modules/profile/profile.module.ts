import { Module } from '@nestjs/common';

import { ProfileService } from './profile.service';
import { ProfileController } from './profile.controller';

import { NetworkingModule } from '@/modules/networking/networking.module';
import { WorkspacesModule } from '@/modules/workspaces/workspaces.module';

@Module({
  imports: [NetworkingModule, WorkspacesModule],
  controllers: [ProfileController],
  providers: [ProfileService],
  exports: [ProfileService],
})
export class ProfileModule {}
