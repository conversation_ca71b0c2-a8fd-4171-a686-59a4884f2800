import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsInt, IsNotEmpty, IsString, Max, <PERSON><PERSON>ength, Min } from 'class-validator';

import { MAX_REVIEW_MESSAGE } from '@/constants/profile';

export class NewReviewDto {
  @ApiProperty({
    name: 'rating',
    description: 'it should be greater than or equal to 1 and less than or equal to 5',
    type: 'number',
    required: true,
    example: 3,
  })
  @Max(5)
  @Min(1)
  @IsInt()
  rating: number;

  @ApiProperty({
    name: 'message',
    type: 'string',
    required: true,
    example: 'Very intelligent',
  })
  @MaxLength(MAX_REVIEW_MESSAGE)
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  message: string;
}
