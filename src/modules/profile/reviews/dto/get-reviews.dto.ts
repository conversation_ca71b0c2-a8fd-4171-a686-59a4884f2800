import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, <PERSON>, Min } from 'class-validator';

import { ReviewSortOrder } from '@/constants/profile';

export class FetchReviewsQueryDto {
  @ApiProperty({
    name: 'sortOrder',
    description: `sort order`,
    required: false,
    enum: ReviewSortOrder,
    example: ReviewSortOrder.RECENT,
  })
  @IsEnum(ReviewSortOrder)
  @IsOptional()
  sortOrder: ReviewSortOrder = ReviewSortOrder.RECENT;

  @ApiProperty({
    description: 'Maximum number of results to return, default is 25',
    example: 25,
    required: false,
    default: 25,
  })
  @Transform(({ value }) => parseInt(value, 10))
  @IsInt()
  @Min(1)
  @Max(50)
  limit: number = 25;

  @ApiProperty({
    description: 'Number of results to skip (offset), used for pagination/infinite scroll',
    example: 1,
    required: false,
    default: 1,
  })
  @Transform(({ value }) => parseInt(value, 10))
  @IsInt()
  @Min(1)
  offset: number = 1;
}
