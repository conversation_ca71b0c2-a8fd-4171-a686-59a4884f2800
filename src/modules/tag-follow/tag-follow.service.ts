import { Inject, Injectable } from '@nestjs/common';
// import { CreateUserFollowerDto } from './dto/create-user-follower.dto';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, lte, sql, inArray, notInArray, desc, ne } from 'drizzle-orm';
import { alias } from 'drizzle-orm/pg-core';

import * as schema from '@/db/schema';
import {
  tagFollowers,
  tags,
  postTags,
  posts,
  postMedias,
  workspaces,
  users,
  linkPosts,
} from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';
import {
  DEFAULT_TAGS_LIMIT,
  MAXIMUM_POSTS_PER_TAG,
  TagFollowMode,
  TagFollowerStatus,
} from '@/constants/tag-follow';
import { PostActiveStatus, PostStatus, PostType } from '@/constants/posts';
import { PostTagsStatus } from '@/constants/post-tags';
import { PostMediaStatus } from '@/constants/post-media';
import { TagStatus } from '@/constants/tags';

// ------------------------------------------------------------

@Injectable()
export class TagFollowService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    // private readonly permissionsService: PermissionsService,
  ) {}

  async createFollower(entityId: string, entityType: EntityType, tagId: string) {
    const tagFollower = await this.drizzleDev.query.tagFollowers.findFirst({
      where: and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.tagId, tagId)),
    });

    if (tagFollower) {
      if (tagFollower.status === TagFollowerStatus.ACTIVE) {
        throw itemAlreadyExists(EntityName.TAG_FOLLOWER);
      } else if (tagFollower.status === TagFollowerStatus.INACTIVE) {
        const [updatedTagFollower] = await this.drizzleDev
          .update(tagFollowers)
          .set({ status: TagFollowerStatus.ACTIVE })
          .where(and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.tagId, tagId)))
          .returning();

        return updatedTagFollower;
      }
    }

    const [newTagFollower] = await this.drizzleDev
      .insert(tagFollowers)
      .values({
        tagId,
        entityId,
        entityType,
        status: TagFollowerStatus.ACTIVE,
      })
      .returning();

    return newTagFollower;
  }

  async softDeleteTagFollower(entityId: string, tagId: string) {
    const [deletedTagFollower] = await this.drizzleDev
      .update(tagFollowers)
      .set({ status: TagFollowerStatus.INACTIVE })
      .where(
        and(
          eq(tagFollowers.entityId, entityId),
          eq(tagFollowers.tagId, tagId),
          eq(tagFollowers.status, TagFollowerStatus.ACTIVE),
        ),
      )
      .returning();

    if (!deletedTagFollower) throw itemNotFound(EntityName.TAG_FOLLOWER);

    return deletedTagFollower;
  }

  async fetchFollowedTagsWithPosts(
    entityId: string,
    offset: number = 0,
    limit: number = DEFAULT_TAGS_LIMIT,
    searchQuery?: string,
  ) {
    const db = this.drizzleDev;

    // CTE: followed_tags
    const followedTagsCte = db.$with('followed_tags').as(
      db
        .select({
          tag_id: tagFollowers.tagId,
          followed_at: tagFollowers.updatedAt, // since we are not removing the entity while unfollowing, updatedAt will be the last followed date
        })
        .from(tagFollowers)
        .where(
          and(
            eq(tagFollowers.entityId, entityId),
            eq(tagFollowers.status, TagFollowerStatus.ACTIVE),
          ),
        ),
    );

    return this.fetchTagsWithPostsCore(
      followedTagsCte,
      searchQuery,
      offset,
      limit,
      TagFollowMode.FOLLOWING,
    );
  }

  // New function for suggested tags
  async fetchSuggestedTagsWithPosts(
    entityId: string,
    offset: number = 0,
    limit: number = DEFAULT_TAGS_LIMIT,
    searchQuery?: string,
  ) {
    const db = this.drizzleDev;

    // CTE: suggested_tags (tags NOT followed by user)
    const suggestedTagsCte = db.$with('suggested_tags').as(
      db
        .select({
          tag_id: tags.id,
          followed_at: sql`NULL::timestamp`.as('followed_at'),
        })
        .from(tags)
        .where(
          and(
            eq(tags.status, TagStatus.ACTIVE),
            // Exclude tags that user is already following
            notInArray(
              tags.id,
              db
                .select({ tag_id: tagFollowers.tagId })
                .from(tagFollowers)
                .where(
                  and(
                    eq(tagFollowers.entityId, entityId),
                    eq(tagFollowers.status, TagFollowerStatus.ACTIVE),
                  ),
                ),
            ),
          ),
        )
        .orderBy(desc(tags.createdAt)) // Order by tag creation date
        .limit(limit)
        .offset(offset),
    );

    return this.fetchTagsWithPostsCore(
      suggestedTagsCte,
      searchQuery,
      offset,
      limit,
      TagFollowMode.SUGGESTED,
    );
  }

  // Shared core logic that both functions use
  private async fetchTagsWithPostsCore(
    tagsCte: any,
    searchQuery?: string,
    offset: number = 0,
    limit: number = 2,
    mode: TagFollowMode = TagFollowMode.FOLLOWING,
  ) {
    const db = this.drizzleDev;

    // Create table aliases for multiple user joins
    const workspaceCreator = alias(users, 'workspace_creator');
    const publishedByUser = alias(users, 'published_by_user');

    // CTE: tags_with_valid_posts
    const tagsWithValidPostsCte = db.$with('tags_with_valid_posts').as(
      db
        .selectDistinct({ tag_id: postTags.tagId })
        .from(postTags)
        .innerJoin(
          posts,
          and(
            eq(postTags.postId, posts.id),
            eq(postTags.status, PostTagsStatus.ACTIVE),
            eq(posts.status, PostActiveStatus.ACTIVE),
            ne(posts.postStatus, PostStatus.DRAFT),
            lte(posts.postScheduleDate, new Date()),
          ),
        )
        .where(inArray(postTags.tagId, db.select({ tag_id: tagsCte.tag_id }).from(tagsCte))),
    );

    // CTE: paginated_tags (only tags with valid posts)
    const paginatedTagsCte = db.$with('paginated_tags').as(
      db
        .select({
          tag_id: tags.id,
          tag_name: tags.name,
          tag_status: tags.status,
          followed_at: tagsCte.followed_at,
        })
        .from(tags)
        .innerJoin(tagsCte, eq(tagsCte.tag_id, tags.id))
        .innerJoin(tagsWithValidPostsCte, eq(tags.id, tagsWithValidPostsCte.tag_id))
        .where(
          and(
            eq(tags.status, TagStatus.ACTIVE),
            searchQuery
              ? sql`LOWER(REPLACE(${tags.name}, ' ', '')) LIKE ${`%${searchQuery}%`}`
              : sql`TRUE`,
          ),
        )
        .orderBy(
          mode === TagFollowMode.FOLLOWING ? desc(tagsCte.followed_at) : desc(tags.createdAt),
        )
        .limit(limit)
        .offset(offset),
    );

    // CTE: posts_per_tag (latest 2 posts per tag)
    const postsPerTagCte = db.$with('posts_per_tag').as(
      db
        .select({
          tag_id: postTags.tagId,
          post_id: posts.id,
          content: posts.content,
          post_type: posts.postType,
          created_at: posts.createdAt,
          workspace_id: posts.workspaceId,
          // Publisher info: prioritize workspaceCreator if exists, as posts created within a workspace
          // are typically attributed to the workspace owner. If no workspace creator is available it means its a public post,
          // so fall back to the user who published the post.
          publisher_display_name:
            sql`COALESCE(${workspaceCreator.displayName}, ${publishedByUser.displayName})`.as(
              'publisher_display_name',
            ),
          publisher_username:
            sql`COALESCE(${workspaceCreator.username}, ${publishedByUser.username})`.as(
              'publisher_username',
            ),
          publisher_profile_image:
            sql`COALESCE(${workspaceCreator.profileImageUrlThumbnail}, ${publishedByUser.profileImageUrlThumbnail})`.as(
              'publisher_profile_image',
            ),
          medias: sql`
            CASE 
          WHEN ${posts.postType} = ${PostType.ARTICLE} THEN (
            ${db
              .select({
                media: sql`json_build_object(
                  'mediaPath', ${postMedias.mediaPath},
                  'mediaType', ${postMedias.mediaType},
                  'altText', ${postMedias.altText}
                )`,
              })
              .from(postMedias)
              .where(
                and(
                  eq(postMedias.postId, posts.id),
                  eq(postMedias.status, PostMediaStatus.ACTIVE),
                  eq(postMedias.isCoverPic, true),
                ),
              )
              .limit(1)}
          )
          WHEN ${posts.postType} = ${PostType.MEDIA} THEN (
            ${db
              .select({
                medias: sql`json_agg(
                  json_build_object(
                    'mediaPath', ${postMedias.mediaPath},
                    'mediaType', ${postMedias.mediaType},
                    'altText', ${postMedias.altText}
                  )
                )`,
              })
              .from(postMedias)
              .where(
                and(eq(postMedias.postId, posts.id), eq(postMedias.status, PostMediaStatus.ACTIVE)),
              )}
          )
          WHEN ${posts.postType} = ${PostType.LINK} THEN (
            ${db
              .select({
                linkData: sql`json_build_object(
                  'link', ${linkPosts.link}
                )`,
              })
              .from(linkPosts)
              .where(eq(linkPosts.postId, posts.id))
              .limit(1)}
          )
          ELSE NULL
        END
          `.as('medias'),
          row_number: sql<number>`
              ROW_NUMBER() OVER (
                PARTITION BY ${postTags.tagId}
                ORDER BY ${posts.createdAt} DESC
              )
            `
            .mapWith(Number)
            .as('row_number'),
        })
        .from(postTags)
        .innerJoin(
          posts,
          and(
            eq(postTags.postId, posts.id),
            eq(postTags.status, PostTagsStatus.ACTIVE),
            eq(posts.status, PostActiveStatus.ACTIVE),
            ne(posts.postStatus, PostStatus.DRAFT),
            lte(posts.postScheduleDate, new Date()),
          ),
        )
        .leftJoin(
          postMedias,
          and(eq(postMedias.postId, posts.id), eq(postMedias.status, PostMediaStatus.ACTIVE)),
        )
        .leftJoin(workspaces, eq(posts.workspaceId, workspaces.id))
        .leftJoin(workspaceCreator, eq(workspaces.createdById, workspaceCreator.id))
        .leftJoin(publishedByUser, eq(posts.publishedById, publishedByUser.id))
        .where(
          inArray(
            postTags.tagId,
            db.select({ tag_id: paginatedTagsCte.tag_id }).from(paginatedTagsCte),
          ),
        )
        .groupBy(
          postTags.tagId,
          posts.id,
          posts.content,
          posts.postType,
          posts.createdAt,
          posts.workspaceId,
          publishedByUser.displayName,
          publishedByUser.username,
          publishedByUser.profileImageUrlThumbnail,
          workspaceCreator.displayName,
          workspaceCreator.username,
          workspaceCreator.profileImageUrlThumbnail,
        ),
    );

    // CTE: final_result (only top 2 posts per tag)
    const finalResultCte = db.$with('final_result').as(
      db
        .select({
          tag_id: sql`${tags.id}`.as('tag_id'),
          tag_name: sql`${tags.name}`.as('tag_name'),
          tag_status: sql`${tags.status}`.as('tag_status'),
          post_id: postsPerTagCte.post_id,
          content: postsPerTagCte.content,
          post_type: postsPerTagCte.post_type,
          created_at: postsPerTagCte.created_at,
          workspace_id: postsPerTagCte.workspace_id,
          publisher_display_name: postsPerTagCte.publisher_display_name,
          publisher_username: postsPerTagCte.publisher_username,
          publisher_profile_image: postsPerTagCte.publisher_profile_image,
          medias: postsPerTagCte.medias,
        })
        .from(tags)
        .innerJoin(paginatedTagsCte, eq(paginatedTagsCte.tag_id, tags.id))
        .innerJoin(
          postsPerTagCte,
          and(
            eq(postsPerTagCte.tag_id, tags.id),
            lte(postsPerTagCte.row_number, MAXIMUM_POSTS_PER_TAG),
          ),
        ),
    );

    // Final query execution
    const result = await db
      .with(tagsCte, tagsWithValidPostsCte, paginatedTagsCte, postsPerTagCte, finalResultCte)
      .select({
        tag_id: finalResultCte.tag_id,
        tag_name: finalResultCte.tag_name,
        tag_status: finalResultCte.tag_status,
        post_id: finalResultCte.post_id,
        content: finalResultCte.content,
        post_type: finalResultCte.post_type,
        workspace_id: finalResultCte.workspace_id,
        publisher_display_name: finalResultCte.publisher_display_name,
        publisher_username: finalResultCte.publisher_username,
        publisher_profile_image: finalResultCte.publisher_profile_image,
        medias: finalResultCte.medias,
      })
      .from(finalResultCte)
      .orderBy(desc(finalResultCte.created_at));

    // Group the results by tag
    const groupedMap = new Map();

    for (const row of result) {
      const tagId = row.tag_id;

      if (!groupedMap.has(tagId)) {
        groupedMap.set(tagId, {
          tagId: row.tag_id,
          tagName: row.tag_name,
          posts: [],
        });
      }

      groupedMap.get(tagId).posts.push({
        postId: row.post_id,
        content: row.content,
        postType: row.post_type,
        workspaceId: row.workspace_id,
        publisherName: row.publisher_display_name,
        username: row.publisher_username,
        profileImageUrlThumbnail: row.publisher_profile_image,
        ...(row.medias ? { medias: row.medias } : {}),
      });
    }

    return Array.from(groupedMap.values());
  }

  // async findFollowStatsOfAWorkspaceById(workspaceId: string, entityId?: string) {
  //   const [{ followingCount }] = await this.drizzleDev
  //     .select({ followingCount: count() })
  //     .from(workspaceFollowers)
  //     .where(
  //       and(
  //         eq(workspaceFollowers.entityId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     );

  //   const [{ followersCount }] = await this.drizzleDev
  //     .select({ followersCount: count() })
  //     .from(workspaceFollowers)
  //     .where(
  //       and(
  //         eq(workspaceFollowers.workspaceId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     );

  //   const response: { followersCount: number; followingCount: number; isFollowing?: boolean } = {
  //     followersCount,
  //     followingCount,
  //   };

  //   if (entityId) {
  //     const isExist = await this.drizzleDev.query.workspaceFollowers.findFirst({
  //       where: and(
  //         eq(workspaceFollowers.entityId, entityId),
  //         eq(workspaceFollowers.workspaceId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     });

  //     if (isExist) response.isFollowing = true;
  //   }

  //   return response;
  // }

  // async findAllFollowingForId(entityId: string) {
  //   return this.drizzleDev.query.workspaceFollowers.findMany({
  //     where: and(
  //       eq(workspaceFollowers.entityId, entityId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //     columns: {},
  //     // eslint-disable-next-line @typescript-eslint/no-shadow
  //     orderBy: (workspaceFollowers, { desc }) => [desc(workspaceFollowers.createdAt)],
  //     with: {
  //       workspace: {
  //         columns: {
  //           id: true,
  //           type: true,
  //           workspacename: true,
  //           label: true,
  //         },
  //       },
  //     },
  //   });
  // }

  // findAllFollowersForId(workspaceId: string) {
  //   return this.drizzleDev.query.workspaceFollowers.findMany({
  //     where: and(
  //       eq(workspaceFollowers.workspaceId, workspaceId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //     columns: {},
  //     // eslint-disable-next-line @typescript-eslint/no-shadow
  //     orderBy: (workspaceFollowers, { desc }) => [desc(workspaceFollowers.createdAt)],
  //     with: {
  //       workspace: {
  //         columns: {
  //           id: true,
  //           type: true,
  //           workspacename: true,
  //           label: true,
  //         },
  //       },
  //     },
  //   });
  // }

  // async isUserOrWorkspaceFollowingParticularWorkspaceById(entityId: string, workspaceId: string) {
  //   const workspaceFollowerQuery = await this.drizzleDev.query.workspaceFollowers.findFirst({
  //     where: and(
  //       eq(workspaceFollowers.entityId, entityId),
  //       eq(workspaceFollowers.workspaceId, workspaceId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //   });

  //   if (workspaceFollowerQuery) return true;

  //   return false;
  // }
}
