import { Module } from '@nestjs/common';
import { StripeController } from './stripe.controller';
import { StripeService } from './stripe.service';
import { SubscriptionsModule } from '../subscriptions/subscriptions.module';
import { CustomConfigService } from '@/config/configuration.service';

@Module({
  imports: [SubscriptionsModule],
  controllers: [StripeController],
  providers: [StripeService, CustomConfigService],
  exports: [StripeService],
})
export class StripeModule {}
