import { Injectable } from '@nestjs/common';
import Strip<PERSON> from 'stripe';
import { SubscriptionsService } from '../subscriptions/subscriptions.service';
import { CustomConfigService } from '@/config/configuration.service';
import { SuccessfulSubscriptionData } from '@/interfaces/subscription';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly customConfigService: CustomConfigService,
  ) {
    this.stripe = new Stripe(this.customConfigService.getStripeConfig().secretKey, {
      apiVersion: '2025-08-27.basil',
    });
  }

  constructWebhookEvent(body: Buffer, signature: string) {
    return this.stripe.webhooks.constructEvent(
      body,
      signature,
      this.customConfigService.getStripeConfig().webhookSecret,
    );
  }

  async findOrCreateCustomer(email: string, userId: string) {
    // First try to find an existing customer by email
    const existing = await this.stripe.customers.list({
      email,
      limit: 1,
    });

    if (existing.data.length > 0) {
      return existing.data[0];
    }

    // Otherwise create new
    return this.stripe.customers.create({
      email,
      metadata: { userId },
    });
  }

  async createSubscriptionCheckout(
    customerId: string,
    priceId: string,
    successUrl: string,
    cancelUrl: string,
    metadata: Record<string, string>,
  ) {
    return this.stripe.checkout.sessions.create({
      customer: customerId,
      line_items: [{ price: priceId, quantity: 1 }],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata,
    });
  }

  async handleSuccessfulSubscription(data: SuccessfulSubscriptionData) {
    // CreateSubscription transaction
    return this.subscriptionsService.createSubscriptionFlow(data);
  }
}
