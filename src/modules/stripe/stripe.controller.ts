import Stripe from 'stripe';
import { ApiTags } from '@nestjs/swagger';
import { Body, Controller, Headers, Post, Req } from '@nestjs/common';
import { Request } from 'express';

import { CreateCheckoutSessionDto } from './dto/create-checkout.dto';

import { AccountType } from '@/constants/users';
import { EntityName } from '@/constants/entities';
import { AuthConstants } from '@/constants/auth';

import { User } from '@/decorators/user.decorator';
import { Public } from '@/decorators/public.decorator';
import { AccountTypes } from '@/decorators/account-types.decorator';

import { StripeService } from './stripe.service';
import { SubscriptionsService } from '@/modules/subscriptions/subscriptions.service';

import { CheckoutMetadata } from '@/interfaces/stripe';

import {
  accountTypeAndSubscriptionPlanNotMatching,
  invalidStripeSignatureException,
  missingStripeMetadataException,
  stripePriceIdNotFoundException,
} from '@/exceptions/subscription';
import { itemNotFound } from '@/exceptions/common';
import { UserSegment } from '@/constants/user-segments';

@Controller('stripe')
@ApiTags('stripe')
export class StripeController {
  constructor(
    private readonly stripeService: StripeService,
    private readonly subscriptionsService: SubscriptionsService,
  ) {}

  @Post('subscribe')
  @AccountTypes(AccountType.ORGANISATION, AccountType.PROFESSIONAL)
  async subscribe(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_EMAIL) userEmail: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_SEGMENT) userSegment: UserSegment,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_UID) firebaseUid: string,
    @Body() dto: CreateCheckoutSessionDto,
  ) {
    const subscriptionPlanAndPrices = await this.subscriptionsService.getSubscriptionPriceIds(
      dto.subscriptionPlanId,
    );

    if (!subscriptionPlanAndPrices) throw itemNotFound(EntityName.SUBSCRIPTION);

    const priceId = dto.isYearly
      ? subscriptionPlanAndPrices.stripePriceYearlyId
      : subscriptionPlanAndPrices.stripePriceMonthlyId;

    if (!priceId) throw stripePriceIdNotFoundException();

    if (userSegment !== subscriptionPlanAndPrices.userSegment) {
      throw accountTypeAndSubscriptionPlanNotMatching();
    }

    const customer = await this.stripeService.findOrCreateCustomer(userEmail, userId);

    const session = await this.stripeService.createSubscriptionCheckout(
      customer.id,
      priceId,
      dto.successUrl,
      dto.cancelUrl,
      {
        subscriptionPlanId: dto.subscriptionPlanId,
        isYearly: String(dto.isYearly),
        stripePriceId: priceId,
        userId,
        workspaceId,
        firebaseUid,
        userSegment,
      },
    );

    return { url: session.url };
  }

  @Post('webhook')
  @Public()
  async handleStripeWebhook(@Req() req: Request, @Headers('stripe-signature') signature: string) {
    let event: Stripe.Event;

    try {
      event = this.stripeService.constructWebhookEvent(req.body as Buffer, signature);
    } catch {
      throw invalidStripeSignatureException();
    }

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;

        if (!session.metadata) {
          throw missingStripeMetadataException();
        }

        const metadata = session.metadata as unknown as CheckoutMetadata;

        const { subscriptionPlanId, isYearly, userId, workspaceId, firebaseUid, userSegment } =
          metadata;

        await this.stripeService.handleSuccessfulSubscription({
          subscriptionPlanId,
          isYearly: isYearly === 'true',
          userId,
          workspaceId,
          firebaseUid,
          transactionId: session.id,
          userSegment,
        });

        break;
      }
    }

    return { received: true };
  }
}
