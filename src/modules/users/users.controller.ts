import { Controller, Get, Param, Delete, BadRequestException } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { User } from '@/db/schema';

import { UsersService } from './users.service';

import { AuthConstants } from '@/constants/auth';
import { EntityName } from '@/constants/entities';
import { Role } from '@/constants/roles';
import { Permissions } from '@/constants/permissions';

import { itemNotFound } from '@/exceptions/common';

import { Permissions as PermissionsDec } from '@/decorators/permissions.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';
import { User as UserDec } from '@/decorators/user.decorator';
import { Public } from '@/decorators/public.decorator';
import { AccountType } from '@/constants/users';
import { AccountTypes } from '@/decorators/account-types.decorator';
import { getAuth } from 'firebase-admin/auth';
import { UserData } from '@/interfaces/auth';

@Controller('users')
@ApiTags('users')
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Retrieve all users
   * @returns An array of all users
   */
  @Get()
  @PermissionsDec(Permissions.VIEW_USERS)
  findAllUsers(): Promise<User[]> {
    return this.usersService.findAllUsers();
  }

  /**
   * Retrieve own user details
   * @userDec userId - The userId of the user from the user object set on req.user
   * @returns An user details
   */
  @Get('profile')
  async findUserOwn(@UserDec(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string) {
    const user = await this.usersService.findUserById(userId);
    if (!user) throw itemNotFound(EntityName.USER);

    const userRecord = await getAuth().getUser(user.providerId);
    const pendingStages = userRecord.customClaims?.[
      AuthConstants.FIREBASE_CLAIM_PENDING_STAGES
    ] as UserData;

    return {
      ...user,
      pendingStages,
    };
  }

  @Get('currentSubscription')
  @AccountTypes(AccountType.ORGANISATION, AccountType.PROFESSIONAL)
  async getCurrentUsersSubscriptionPlan(
    @UserDec(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const userSubscription =
      await this.usersService.findUsersSubscriptionByWorkspaceId(workspaceId);
    if (!userSubscription) throw itemNotFound(EntityName.SUBSCRIPTION);

    return userSubscription;
  }

  @Get(':id')
  @PermissionsDec(Permissions.VIEW_USERS)
  async findUserById(@Param('id') id: string) {
    if (!id) throw new BadRequestException('User ID is required');
    const user = await this.usersService.findUserById(id);
    if (!user) throw itemNotFound(EntityName.USER);
    return user;
  }

  /**
   * Find a user by their username
   * @param username - The username of the user to find
   * @returns The user with the specified username
   * @throws NotFoundException if user is not found
   */
  @Public()
  @Get('username/:username')
  async findUserByUsername(@Param('username') username: string) {
    const user = await this.usersService.findUserByUsername(username);
    if (!user) throw itemNotFound(EntityName.USER);
    return user;
  }

  /**
   * Find a user by their email
   * @param email - The email of the user to find
   * @returns The user with the specified email
   * @throws BadRequestException if email is not provided
   * @throws NotFoundException if user is not found
   */
  @Get(':email')
  @UserRoles(Role.SUPER_ADMIN)
  @PermissionsDec(Permissions.VIEW_USERS)
  async findUserByEmail(@Param('email') email: string) {
    if (!email) throw new BadRequestException('User email is required');
    const user = await this.usersService.findUserByEmail(email);
    if (!user) throw itemNotFound(EntityName.USER);
    return user;
  }

  /**
   * Remove a user
   * @param id - The ID of the user to remove
   * @returns The removed user
   */
  @Delete(':id')
  @PermissionsDec(Permissions.DELETE_USER)
  removeUser(@Param('id') id: string): Promise<User> {
    return this.usersService.removeUser(id);
  }
}
