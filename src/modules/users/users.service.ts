import { Injectable, Inject } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, gte, ne, SQL } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  users,
  userPermissions,
  userRoles,
  subscriptionPlans,
  workspaceSubscriptions,
} from '@/db/schema';

import { itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { Role } from '@/constants/roles';
import { UserRolesStatus } from '@/constants/user-roles';
import { Permissions } from '@/constants/permissions';
import { UserStatus } from '@/constants/users';
import { UserPermissionStatus } from '@/constants/user-permissions';

import { GlobalPermission, WorkspacePermission } from '@/interfaces/user';
import { WorkspaceSubscriptionsStatus } from '@/constants/workspace-subscriptions';

@Injectable()
export class UsersService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async findAllUsers() {
    return this.drizzleDev.query.users.findMany({
      where: eq(users.status, 1),
    });
  }

  findUserById(id: string, txn?: PostgresJsDatabase<typeof schema>) {
    return this.findUserByCondition(eq(users.id, id), txn);
  }

  findUserByUsername(username: string, txn?: PostgresJsDatabase<typeof schema>) {
    return this.findUserByCondition(eq(users.username, username), txn);
  }

  findUserByEmail(email: string, txn?: PostgresJsDatabase<typeof schema>) {
    return this.findUserByCondition(eq(users.email, email), txn);
  }

  private async findUserByCondition(condition: SQL, txn?: PostgresJsDatabase<typeof schema>) {
    const dbOrTransaction = txn || this.drizzleDev;

    //TODO: ENSURE THAT THE JOINING TABLE HAS ACTIVE STATUS
    const user = await dbOrTransaction.query.users.findFirst({
      where: and(condition, eq(users.status, 1)),
      with: {
        userRoles: {
          where: eq(userRoles.status, UserRolesStatus.ACTIVE),
          columns: {
            id: true,
          },
          with: {
            role: {
              columns: {
                name: true,
                key: true,
              },
            },
            userPermissions: {
              where: eq(userPermissions.status, UserPermissionStatus.ACTIVE),
              with: {
                permission: {
                  columns: {
                    key: true,
                  },
                },
              },
              columns: {},
            },
            workspaces: {
              columns: {
                id: true,
                status: true,
                primarySpeciality: true,
              },
            },
          },
        },
      },
    });

    if (!user) return null;

    // Define the structure of the updated format
    type UpdatedFormat = Omit<typeof user, 'userRoles'> & {
      permissions: {
        global: GlobalPermission[];
        workspace: WorkspacePermission[];
      };
      userRoles?: (typeof user)['userRoles']; // Dynamically reference the type from user
    };

    const updatedFormat: UpdatedFormat = {
      ...user,
      permissions: {
        global: [] as GlobalPermission[], // Explicit type for global roles
        workspace: [] as WorkspacePermission[], // Explicit type for workspace roles
      },
    };

    // Process the roles and permissions
    user?.userRoles?.forEach((userRole) => {
      const roleKey = userRole.role.key as Role;
      const roleName = userRole.role.name;
      const permissions = userRole.userPermissions.map(
        (permission) => permission.permission.key as Permissions,
      );

      if (!userRole.workspaces?.id) {
        // It's a global role, add to the global section
        updatedFormat.permissions.global.push({
          id: userRole.id,
          name: user.displayName,
          roleName,
          roleKey,
          permissions,
        });
      } else {
        // It's a workspace role, add to the workspace section
        updatedFormat.permissions.workspace.push({
          id: userRole.id,
          workspaceId: userRole.workspaces?.id ?? '',
          status: userRole.workspaces?.status,
          roleName,
          roleKey,
          permissions,
          primarySpeciality: userRole.workspaces.primarySpeciality,
        });
      }
    });

    delete updatedFormat.userRoles;

    return updatedFormat;
  }

  async removeUser(id: string) {
    const [deletedUser] = await this.drizzleDev
      .update(users)
      .set({
        status: UserStatus.ACTIVE,
      })
      .where(and(eq(users.id, id), ne(users.status, UserStatus.ARCHIVED)))
      .returning();

    if (!deletedUser) throw itemNotFound(EntityName.USER);

    return deletedUser;
  }

  async findUsersSubscriptionByWorkspaceId(
    workspaceId: string,
    txn?: PostgresJsDatabase<typeof schema>,
  ): Promise<string | null> {
    const dbOrTransaction = txn || this.drizzleDev;

    const today = new Date();
    today.setUTCHours(0, 0, 0, 0);

    const subscription = await dbOrTransaction
      .select({
        title: subscriptionPlans.title,
      })
      .from(workspaceSubscriptions)
      .leftJoin(
        subscriptionPlans,
        eq(workspaceSubscriptions.subscriptionPlanId, subscriptionPlans.id),
      )
      .where(
        and(
          eq(workspaceSubscriptions.workspaceId, workspaceId),
          eq(workspaceSubscriptions.status, WorkspaceSubscriptionsStatus.ACTIVE),
          gte(workspaceSubscriptions.endDate, today.toISOString()),
        ),
      )
      .limit(1);

    return subscription[0]?.title ?? null;
  }
}
