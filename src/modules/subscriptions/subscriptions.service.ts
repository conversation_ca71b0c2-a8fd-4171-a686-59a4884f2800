import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  subscriptionPlans,
  workspaceSubscriptions,
  prestigeMembershipApplications,
  organisations,
} from '@/db/schema';

import { UserSegment } from '@/constants/user-segments';
import { SubscriptionPlan, SubscriptionPlanStatus } from '@/constants/subscription-plans';
import { PaymentMethod, WorkspaceSubscriptionsStatus } from '@/constants/workspace-subscriptions';
import { PrestigeMembershipApplicationStatus } from '@/constants/prestige-membership-applications';

import { calculateSubscriptionEndDate } from '@/utils/subscriptions';
import { UsersService } from '../users/users.service';
import { WorkspacesService } from '../workspaces/workspaces.service';
import { itemNotFound } from '@/exceptions/common';
import { EntityName } from '@/constants/entities';
import {
  accountTypeAndSubscriptionPlanNotMatching,
  organizationApplicationFieldsRequired,
} from '@/exceptions/subscription';
import { getAuth } from 'firebase-admin/auth';
import { UserData } from '@/interfaces/auth';
import { AccountSetupStage } from '@/constants/users';
import { AuthConstants } from '@/constants/auth';
import { PrestigeData } from '@/interfaces/subscription';

@Injectable()
export class SubscriptionsService {
  constructor(
    private readonly usersService: UsersService,
    private readonly workspacesService: WorkspacesService,
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
  ) {}

  findSubscriptionPlansByUserSegment({ userSegment }: { userSegment: UserSegment }) {
    return this.drizzleDev.query.subscriptionPlans.findMany({
      where: and(
        eq(subscriptionPlans.userSegment, userSegment),
        eq(subscriptionPlans.status, SubscriptionPlanStatus.ACTIVE),
      ),
      orderBy: (sub, { asc }) => [asc(sub.priceMonthly)],
      with: {
        planFeatures: {
          columns: {
            booleanValue: true,
            integerValue: true,
            textValue: true,
          },
          with: {
            subscriptionFeature: {
              columns: {
                id: true,
                name: true,
                type: true,
                description: true,
              },
            },
          },
        },
      },
    });
  }

  async findSubscriptionPlanById(subscriptionId: string) {
    const subscriptionPlan = await this.drizzleDev.query.subscriptionPlans.findFirst({
      where: and(
        eq(subscriptionPlans.id, subscriptionId),
        eq(subscriptionPlans.status, SubscriptionPlanStatus.ACTIVE),
      ),
    });

    if (!subscriptionPlan) return null;

    return subscriptionPlan;
  }

  // Fetching only Stripe Price IDs
  async getSubscriptionPriceIds(subscriptionId: string) {
    const subscriptionPlan = await this.drizzleDev.query.subscriptionPlans.findFirst({
      where: and(
        eq(subscriptionPlans.id, subscriptionId),
        eq(subscriptionPlans.status, SubscriptionPlanStatus.ACTIVE),
      ),
    });

    if (!subscriptionPlan) return null;

    return {
      userSegment: subscriptionPlan.userSegment,
      stripePriceMonthlyId: subscriptionPlan.stripePriceMonthlyId,
      stripePriceYearlyId: subscriptionPlan.stripePriceYearlyId,
    };
  }

  async createSubscripton(
    {
      subscriptionPlanId,
      isYearly,
      workspaceId,
      transactionId,
    }: {
      subscriptionPlanId: string;
      isYearly: boolean;
      workspaceId: string;
      transactionId?: string;
    },
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const now = new Date();
    const endDate = calculateSubscriptionEndDate(isYearly);

    const subscriptionWorkspacesData: schema.NewWorkspaceSubscription = {
      workspaceId,
      subscriptionPlanId,
      isYearly,
      paymentMethod: transactionId ? PaymentMethod.STRIPE : PaymentMethod.FREE,
      transactionId: transactionId ?? null,
      endDate: endDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
      paymentDate: now.toISOString().split('T')[0],
      startDate: now.toISOString().split('T')[0],
      status: WorkspaceSubscriptionsStatus.ACTIVE,
    };

    const [subscription] = await dbOrTransaction
      .insert(workspaceSubscriptions)
      .values(subscriptionWorkspacesData)
      .returning();

    return subscription;
  }

  async findExistingPrestigeMembershipApplication(workspaceId: string) {
    return this.drizzleDev.query.prestigeMembershipApplications.findFirst({
      where: eq(prestigeMembershipApplications.workspaceId, workspaceId),
    });
  }

  async applyForPrestigeMembership(
    {
      workspaceId,
      website,
      organisationSize,
      pointOfContactName,
      pointOfContactPhone,
      allowSubsidiaries,
    }: {
      workspaceId: string;
      website?: string;
      organisationSize?: string;
      pointOfContactName?: string;
      pointOfContactPhone?: string;
      allowSubsidiaries?: boolean;
    },
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const [application] = await dbOrTransaction
      .insert(prestigeMembershipApplications)
      .values({
        workspaceId,
        website,
        organisationSize,
        pointOfContactName,
        pointOfContactPhone,
        allowSubsidiaries,
        status: PrestigeMembershipApplicationStatus.PENDING,
      })
      .returning();

    return application;
  }

  async savePrestigeData(
    {
      website,
      organisationSize,
      pointOfContactName,
      pointOfContactPhone,
      allowSubsidiaries,
      pointOfContactEmail,
    }: {
      website: string;
      organisationSize: string;
      pointOfContactName: string;
      pointOfContactPhone: string;
      pointOfContactEmail: string;
      allowSubsidiaries: boolean;
    },
    workspaceId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const prestigeData = {
      website,
      organisationSize,
      pointOfContactName,
      pointOfContactPhone,
      pointOfContactEmail,
      allowSubsidiaries,
    };

    const updatedData = {
      ...prestigeData,
      status: SubscriptionPlanStatus.ACTIVE,
    };

    const [updatedOrganisation] = await dbOrTransaction
      .update(organisations)
      .set(updatedData)
      .where(eq(organisations.workspaceId, workspaceId))
      .returning();

    return updatedOrganisation;
  }

  async createSubscriptionFlow(data: {
    userId: string;
    workspaceId: string;
    firebaseUid: string;
    userSegment: UserSegment;
    subscriptionPlanId: string;
    isYearly: boolean;
    prestigeData?: PrestigeData;
    transactionId?: string;
  }) {
    const {
      userId,
      workspaceId,
      firebaseUid,
      userSegment,
      subscriptionPlanId,
      isYearly,
      prestigeData,
      transactionId,
    } = data;

    // validate user
    const user = await this.usersService.findUserById(userId);
    if (!user) throw itemNotFound(EntityName.USER);

    // validate workspace
    const workspace = await this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true);
    if (!workspace) throw itemNotFound(EntityName.WORKSPACE);

    // validate subscription plan
    const subscriptionDetails = await this.findSubscriptionPlanById(subscriptionPlanId);
    if (!subscriptionDetails) throw itemNotFound(EntityName.SUBSCRIPTION);

    if (userSegment !== subscriptionDetails.userSegment) {
      throw accountTypeAndSubscriptionPlanNotMatching();
    }

    // transaction
    const result = await this.drizzleDev.transaction(async (txn) => {
      const subscription = await this.createSubscripton(
        { subscriptionPlanId, isYearly, workspaceId, transactionId },
        txn,
      );

      // prestige logic
      if (
        subscriptionDetails.title === SubscriptionPlan.PRESTIGE &&
        subscriptionDetails.userSegment === UserSegment.ORGANISATION
      ) {
        if (
          !prestigeData ||
          !prestigeData.website ||
          !prestigeData.organisationSize ||
          !prestigeData.pointOfContactName ||
          !prestigeData.pointOfContactPhone ||
          !prestigeData.pointOfContactEmail ||
          prestigeData.allowSubsidiaries === undefined
        ) {
          throw organizationApplicationFieldsRequired();
        }

        await this.savePrestigeData(
          {
            website: prestigeData.website,
            organisationSize: prestigeData.organisationSize,
            pointOfContactName: prestigeData.pointOfContactName,
            pointOfContactPhone: prestigeData.pointOfContactPhone,
            pointOfContactEmail: prestigeData.pointOfContactEmail,
            allowSubsidiaries: prestigeData.allowSubsidiaries,
          },
          workspaceId,
          txn,
        );
      }

      // update Firebase claims
      const userRecord = await getAuth().getUser(firebaseUid);
      const currentClaims: UserData = userRecord.customClaims as UserData;

      const newClaims = {
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.COMPLETED,
        [AuthConstants.FIREBASE_CLAIM_SUBSCRIPTION_PLAN_TITLE]: subscriptionDetails.title,
        [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [],
      };

      await getAuth().setCustomUserClaims(firebaseUid, { ...currentClaims, ...newClaims });

      const customToken = await getAuth().createCustomToken(firebaseUid);

      return {
        workspaceId,
        subscriptionId: subscription.id,
        redirectToProfileSetup: true,
        currentStage: AccountSetupStage.COMPLETED,
        customToken,
      };
    });

    return result;
  }
}
