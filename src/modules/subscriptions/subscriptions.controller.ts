import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { User } from '@/decorators/user.decorator';
import { AccountTypes } from '@/decorators/account-types.decorator';

import { AuthConstants } from '@/constants/auth';
import { AccountType } from '@/constants/users';
import { EntityName } from '@/constants/entities';
import { PrestigeMembershipApplicationStatus } from '@/constants/prestige-membership-applications';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import { organizationApplicationFieldsRequired } from '@/exceptions/subscription';

import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { GetSubscriptionsDto } from './dto/get-subscriptions.dto';
import { ApplyPrestigeMembershipDto } from './dto/apply-prestige-membership.dto';

import { SubscriptionsService } from './subscriptions.service';
import { WorkspacesService } from '@/modules/workspaces/workspaces.service';
import { UserSegment } from '@/constants/user-segments';

@Controller('subscription-plans')
@ApiBearerAuth()
@ApiTags('subscription-plans')
export class SubscriptionsController {
  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly workspacesService: WorkspacesService,
  ) {}

  @Get(':userSegment')
  @AccountTypes(AccountType.ORGANISATION, AccountType.PROFESSIONAL)
  // @AccountSetupStages(AccountSetupStage.SUBSCRIPTION)
  getSubscriptionDetails(@Param() params: GetSubscriptionsDto) {
    return this.subscriptionsService.findSubscriptionPlansByUserSegment({
      userSegment: params.userSegment,
    });
  }

  // Use this endpoint to create non-payment related subcriptions
  @Post()
  @AccountTypes(AccountType.ORGANISATION, AccountType.PROFESSIONAL)
  // @AccountSetupStages(AccountSetupStage.SUBSCRIPTION)
  async createSubscription(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_SEGMENT) userSegment: UserSegment,
    @User(AuthConstants.FIREBASE_UID) firebaseUid: string,
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ) {
    return this.subscriptionsService.createSubscriptionFlow({
      userId,
      workspaceId,
      firebaseUid,
      userSegment,
      subscriptionPlanId: createSubscriptionDto.subscriptionPlanId,
      isYearly: createSubscriptionDto.isYearly,
      prestigeData: createSubscriptionDto.prestigeData,
    });
  }

  @Post('apply-prestige')
  @AccountTypes(AccountType.ORGANISATION, AccountType.PROFESSIONAL)
  async applyForPrestigeMembership(
    @User(AuthConstants.FIREBASE_CLAIM_ACCOUNT_TYPE) accountType: AccountType,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() applyDto: ApplyPrestigeMembershipDto,
  ) {
    // Validate workspace exists
    const workspace = await this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true);
    if (!workspace) {
      throw itemNotFound(EntityName.WORKSPACE);
    }

    // Check if workspace has already applied
    const existingApplication =
      await this.subscriptionsService.findExistingPrestigeMembershipApplication(workspaceId);

    if (
      existingApplication &&
      existingApplication?.status >= PrestigeMembershipApplicationStatus.PENDING
    ) {
      throw itemAlreadyExists(EntityName.PRESTIGE_MEMBERSHIP_APPLICATION);
    }

    // For organizations, validate required fields
    if (accountType === AccountType.ORGANISATION) {
      if (
        !applyDto.website ||
        !applyDto.organisationSize ||
        !applyDto.pointOfContactName ||
        !applyDto.pointOfContactPhone ||
        !applyDto.allowSubsidiaries
      ) {
        throw organizationApplicationFieldsRequired();
      }
    }

    // Apply for prestige membership
    const application = await this.subscriptionsService.applyForPrestigeMembership({
      workspaceId,
      website: applyDto.website,
      organisationSize: applyDto.organisationSize,
      pointOfContactName: applyDto.pointOfContactName,
      pointOfContactPhone: applyDto.pointOfContactPhone,
      allowSubsidiaries: applyDto.allowSubsidiaries,
    });

    return {
      applicationId: application.id,
    };
  }
}
