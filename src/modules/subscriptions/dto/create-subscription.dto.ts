import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsUUID, ValidateNested } from 'class-validator';

import { ApplyPrestigeMembershipDto } from './apply-prestige-membership.dto';
import { Type } from 'class-transformer';

export class CreateSubscriptionDto {
  @ApiProperty({
    name: 'subscriptionPlanId',
    type: 'uuid',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  subscriptionPlanId: string;

  @ApiProperty({
    name: 'isYearly',
    type: 'boolean',
    required: true,
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  isYearly: boolean;

  @ApiProperty({
    required: false,
    type: ApplyPrestigeMembershipDto,
    description: 'Only required if applying for Prestige membership',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ApplyPrestigeMembershipDto)
  prestigeData?: ApplyPrestigeMembershipDto;
}
