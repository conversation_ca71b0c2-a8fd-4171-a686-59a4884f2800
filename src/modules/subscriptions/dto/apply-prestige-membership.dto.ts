import { IsBoolean, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { MAX_LENGTH, MIN_LENGTH } from '@/constants/posts';

export class ApplyPrestigeMembershipDto {
  @ApiProperty({
    description: 'Organization website',
    required: false,
    example: 'https://example.com',
  })
  @IsString()
  @IsOptional()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  website?: string;

  @ApiProperty({
    description: 'Organization size',
    required: false,
    example: '50-100',
  })
  @IsString()
  @IsOptional()
  @MaxLength(MAX_LENGTH)
  organisationSize?: string;

  @ApiProperty({
    description: 'Name of the point of contact',
    required: false,
    example: 'John Doe',
  })
  @IsString()
  @IsOptional()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  pointOfContactName?: string;

  @ApiProperty({
    description: 'Phone number of the point of contact',
    required: false,
    example: '+**********',
  })
  @IsString()
  @IsOptional()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  pointOfContactPhone?: string;

  @ApiProperty({
    description: 'Email of the point of contact',
    required: false,
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  pointOfContactEmail?: string;

  @ApiProperty({
    description: 'wants subsidiary accounts or not',
    required: false,
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  allowSubsidiaries?: boolean;
}
