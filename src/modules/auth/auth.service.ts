import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';
import { getAuth } from 'firebase-admin/auth';

import { UserData, authUserData } from '@/interfaces/auth';

import { CreateUserRegisterDto } from './dto/create-register.dto';
import { GenerateOtpDto } from './dto/generate-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';

import * as schema from '@/db/schema';
import { users, NewUser, User, otps } from '@/db/schema';

import { verifyToken } from '@/helpers/auth.helpers';

import { AuthConstants } from '@/constants/auth';
import { EntityName } from '@/constants/entities';
import { AccountSetupStage, AccountType, UserStatus } from '@/constants/users';
import { OTP_EXPIRATION_MINUTES, OtpStatus, OtpType } from '@/constants/otp';
import { PrimarySpeciality } from '@/constants/workspaces';
import { PUBLIC_ACCOUNT_TYPE_ROLES_MAP } from '@/constants/roles';

import { calculateOtpExpiry, generateOtp } from '@/utils/otp';

import { MailService } from '@/common/mail/mail.service';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import { emailRegisteredNotVerified, invalidOtp, otpExpired } from '@/exceptions/auth';

import { WorkspacesService } from '@/modules/workspaces/workspaces.service';
import { WorkspaceUsersService } from '@/modules/workspace-users/workspace-users.service';
import { CustomConfigService } from '@/config/configuration.service';

@Injectable()
export class AuthService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly mailService: MailService,
    private readonly workspacesService: WorkspacesService,
    private readonly workspaceUsersService: WorkspaceUsersService,
    private readonly customConfigService: CustomConfigService,
  ) {}

  async registerUser(
    createRegisterDto: CreateUserRegisterDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;
    const { email, token, password } = createRegisterDto;

    // Check Firebase status
    const firebaseStatus = await this.checkFirebaseUserRegistrationAndVerification(email);

    if (firebaseStatus.isRegistered) {
      throw firebaseStatus.isVerified
        ? itemAlreadyExists(EntityName.USER)
        : emailRegisteredNotVerified();
    }

    let providerId: string = '';

    try {
      return await dbOrTransaction.transaction(async (txn) => {
        // Create user
        const newUser = await this.createUserRecord(
          {
            ...createRegisterDto,
            providerId: token
              ? (await verifyToken<authUserData>(token)).uid!
              : (firebaseStatus.userUid ?? ''),
          },
          token ? undefined : password,
          txn,
        );

        providerId = newUser.providerId;

        // checking if the account is organisation or professional
        if (
          PUBLIC_ACCOUNT_TYPE_ROLES_MAP[
            newUser.accountType as AccountType.ORGANISATION | AccountType.PROFESSIONAL
          ]
        ) {
          // Create workspace for them
          const userSegment =
            PUBLIC_ACCOUNT_TYPE_ROLES_MAP[
              newUser.accountType as AccountType.ORGANISATION | AccountType.PROFESSIONAL
            ][0];

          // Create workspace
          const newWorkspace = await this.workspacesService.createWorkspace(
            {
              userSegment,
              createdById: newUser.id,
              primarySpeciality: PrimarySpeciality.BOTH, // Setting to BOTH as default value, will be updated during subscription creation or onboarding
            },
            txn,
          );

          // Associate user with workspace
          await this.workspaceUsersService.create(
            { workspaceId: newWorkspace.id, userId: newUser.id },
            txn,
          );

          // here we are avoiding assigning role, permission for now, since we don't know the segment yet and we do it while subscription is created
          // Update Firebase claims
          await this.setUserFirebaseClaims(newUser, newWorkspace.id);
        } else {
          // Set Firebase claims for non-workspace users
          await this.setUserFirebaseClaims(newUser);
        }

        // Send verification email if not using token - which means not provider (google, apple etc) signin
        // For email/password signup, generate and send OTP

        if (!token) {
          await this.generateOtp({ email }, OtpType.SIGNUP, txn);
        }

        return {
          userId: newUser.id,
          email: newUser.email,
          requiresOTP: !token, // Social logins don't require OTP
        };
      });
    } catch (error) {
      if (providerId) {
        await getAuth().deleteUser(providerId);
      }
      throw error;
    }
  }

  async checkFirebaseUserRegistrationAndVerification(email: string) {
    try {
      // Attempt to retrieve the user from Firebase
      const user = await getAuth().getUserByEmail(email);

      const isRegistered = Boolean(user?.customClaims?.[AuthConstants.FIREBASE_CLAIM_USER_ID]);
      const isVerified = isRegistered && user.emailVerified;

      return {
        userUid: user.uid,
        isRegistered,
        isVerified,
      };
    } catch (error) {
      // Handle only "user not found" errors gracefully
      if (error.code === 'auth/user-not-found') {
        return {
          userUid: '',
          isRegistered: false,
          isVerified: false,
        };
      }

      // Re-throw other errors
      throw error;
    }
  }

  // Core functionality for creating a Firebase user
  async createFirebaseUser(
    email: string,
    displayName: string,
    password?: string,
    emailVerified = false,
  ): Promise<string> {
    const firebaseUser = await getAuth().createUser({
      email,
      displayName,
      password,
      emailVerified,
    });

    return firebaseUser.uid;
  }

  // Centralized method to set Firebase custom claims
  async setUserFirebaseClaims(user: User, workspaceId: string | null = null) {
    if (!user) throw itemNotFound(EntityName.USER);

    const additionalClaims: UserData = {
      [AuthConstants.FIREBASE_CLAIM_USER_ID]: user.id,
      [AuthConstants.FIREBASE_CLAIM_EMAIL]: user.email,
      [AuthConstants.FIREBASE_CLAIM_ROLE]: null,
      [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: [],
      [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: workspaceId,
      [AuthConstants.FIREBASE_CLAIM_ACCOUNT_TYPE]: user.accountType,
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: user.currentStage,
      [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [
        AccountSetupStage.ADDING_NETWORK,
        AccountSetupStage.SUBSCRIPTION,
        AccountSetupStage.DOCUMENT_UPLOAD,
        AccountSetupStage.PROFILE_SETUP,
      ],
      [AuthConstants.FIREBASE_CLAIM_USER_SEGMENT]: undefined,
    };

    await getAuth().setCustomUserClaims(user.providerId, additionalClaims);

    return additionalClaims;
  }

  // Core method for creating a user in your database
  async createUserRecord(
    userDetails: Omit<NewUser, 'username'>,
    password?: string,
    txn?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = txn || this.drizzleDev;
    const { displayName, email, providerId } = userDetails;

    try {
      const firebaseUid = Boolean(providerId)
        ? providerId
        : await this.createFirebaseUser(email, displayName, password);

      // Generate UUID first
      const userId = crypto.randomUUID();

      // Generate username using the UUID
      const cleanedUsername = displayName
        .trim()
        .replace(/[^a-zA-Z0-9_-]/g, '')
        .toLowerCase();

      const username = `${cleanedUsername}-${userId}`;

      const [insertedUser] = await db
        .insert(users)
        .values({
          ...userDetails,
          id: userId,
          username,
          providerId: firebaseUid,
          currentStage: AccountSetupStage.PROFILE_SETUP,
          status: UserStatus.ACTIVE,
        })
        .returning();

      return insertedUser;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate an OTP for registration or sign-in
   */
  async generateOtp(
    data: GenerateOtpDto,
    type: OtpType,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const { email } = data;

    // Generate a 6-digit OTP
    const otpCode = generateOtp();

    // Calculate expiration time (5 minutes from now)
    const expiresAt = calculateOtpExpiry(OTP_EXPIRATION_MINUTES);

    const db = transaction || this.drizzleDev;

    await db.transaction(async (txn) => {
      // Check if the user exists in the database
      const [user] = await db.select().from(users).where(eq(users.email, email)).limit(1);

      if (!user) {
        throw itemNotFound(EntityName.USER);
      }

      // Invalidate any existing active OTPs for this email and type
      await txn
        .update(otps)
        .set({ status: OtpStatus.EXPIRED })
        .where(
          and(eq(otps.userId, user.id), eq(otps.type, type), eq(otps.status, OtpStatus.ACTIVE)),
        );

      // Create a new OTP record
      await txn
        .insert(otps)
        .values({ userId: user.id, otp: otpCode, type, expiresAt, status: OtpStatus.ACTIVE });
    });

    const { isProduction, isStaging } = this.customConfigService.getEnvironment();

    if (isProduction || isStaging) {
      await this.mailService.sendOtpEmail(email, otpCode);
    }

    return { success: true };
  }

  /**
   * Verify a user's email with OTP
   * This should be called after the user has registered and received an OTP
   */
  async verifyUserEmail(email: string, otp: string) {
    const firebaseStatus = await this.checkFirebaseUserRegistrationAndVerification(email);

    if (firebaseStatus.isVerified) {
      throw itemAlreadyExists(EntityName.USER);
    }

    // Verify the OTP
    const verificationResult = await this.verifyOtp({ email, otp });

    if (!verificationResult.verified) {
      throw invalidOtp();
    }

    try {
      // Get the Firebase user
      const firebaseUser = await getAuth().getUserByEmail(email);

      // Mark the user's email as verified
      await getAuth().updateUser(firebaseUser.uid, { emailVerified: true });

      const customToken = await getAuth().createCustomToken(firebaseUser.uid);

      return { customToken };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Verify an OTP for registration
   */
  async verifyOtp(data: VerifyOtpDto) {
    const { email, otp } = data;

    // Check if the user exists in the database
    const [user] = await this.drizzleDev
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (!user) {
      throw itemNotFound(EntityName.USER);
    }

    // Find the active OTP for this email
    const [otpRecord] = await this.drizzleDev
      .select()
      .from(otps)
      .where(
        and(
          eq(otps.userId, user.id),
          eq(otps.otp, otp),
          eq(otps.type, OtpType.SIGNUP),
          eq(otps.status, OtpStatus.ACTIVE),
          eq(otps.used, false),
        ),
      );

    if (!otpRecord) {
      throw invalidOtp();
    }

    // Check if OTP is expired
    if (new Date() > new Date(otpRecord.expiresAt)) {
      // Mark as expired
      await this.drizzleDev
        .update(otps)
        .set({ status: OtpStatus.EXPIRED })
        .where(eq(otps.id, otpRecord.id));

      throw otpExpired();
    }

    // Mark OTP as used
    await this.drizzleDev.update(otps).set({ used: true }).where(eq(otps.id, otpRecord.id));

    return { success: true, verified: true };
  }
}
