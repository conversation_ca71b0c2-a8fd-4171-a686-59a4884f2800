import { Module } from '@nestjs/common';

import { LocationService } from './location.service';
import { LocationController } from './location.controller';
import { CustomConfigService } from '@/config/configuration.service';

@Module({
  imports: [],
  controllers: [LocationController],
  providers: [LocationService, CustomConfigService],
  exports: [LocationService],
})
export class LocationModule {}
