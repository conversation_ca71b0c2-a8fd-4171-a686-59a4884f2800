import { Injectable, Inject } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
// import axios from 'axios';

import * as schema from '@/db/schema';
import { locations, regions } from '@/db/schema';

import { CustomConfigService } from '@/config/configuration.service';
import { FeatureCollection, LocationResult, MapboxGeocodingResponse } from '@/interfaces/location';
import { MAPBOX_GEOCODING_URL, MAPBOX_REVERSE_GEOCODING_URL } from '@/constants/mapbox';

@Injectable()
export class LocationService {
  private readonly mapboxConfig: {
    token: string;
  };

  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly customConfigService: CustomConfigService,
  ) {
    this.mapboxConfig = this.customConfigService.getMapboxConfig();
  }

  /**Fetch suggestions (autocomplete)**/
  async getLocationSuggestions(query: string) {
    if (!query) return [];

    const endpoint = `${MAPBOX_GEOCODING_URL}/${encodeURIComponent(query)}.json`;
    const { data }: { data: MapboxGeocodingResponse } = await axios.get(endpoint, {
      params: {
        access_token: this.mapboxConfig.token,
        autocomplete: true,
        types: 'place,locality,neighborhood,district,region,country',
        limit: 5,
      },
    });

    return data.features.map((place) => {
      const context = place.context || [];
      const getContext = (type: string) => context.find((c) => c.id.startsWith(type))?.text || '';

      // Place, distric, locality all of this required for FE so user can select correct palce
      return {
        mapboxId: place.properties?.mapbox_id || place.id,
        placeId: place.id,
        name: place.text,
        locality: getContext('locality'),
        neighborhood: getContext('neighborhood'),
        district: getContext('district'),
        city: getContext('place') || getContext('locality') || getContext('district'),
        region: getContext('region'),
        country: getContext('country'),
        continent: getContext('continent'),
        latitude: place.center[1],
        longitude: place.center[0],
        fullLabel: place.place_name,
      };
    });
  }

  async getLocationByLatLong(latitude: number, longitude: number) {
    const url = `${MAPBOX_REVERSE_GEOCODING_URL}?longitude=${longitude}&latitude=${latitude}&types=place,region,country&access_token=${this.mapboxConfig.token}`;

    const { data }: { data: FeatureCollection } = await axios.get(url);

    if (!data.features || data.features.length === 0) {
      return null;
    }

    const result: LocationResult = {};

    data.features.forEach((feature) => {
      const type = feature.properties.feature_type;
      result[type] = {
        name: feature.properties.name,
        coordinates: feature.properties.coordinates,
        mapbox_id: feature.properties.mapbox_id,
      };
    });

    return result;
  }

  /**
   * Insert location + regions into DB (idempotent)
   */
  async saveLocation(
    coords: { latitude: number; longitude: number },
    place_id: string,
    transaction?: typeof this.drizzleDev,
  ) {
    const normalized: LocationResult | null = await this.getLocationByLatLong(
      coords.latitude,
      coords.longitude,
    );
    if (!normalized)
      return {
        locationId: null,
        regionId: null,
      };

    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (txn) => {
      // Upsert Country
      const [country] = normalized.country
        ? await txn
            .insert(regions)
            .values({
              name: normalized.country.name,
              type: 'country',
              latitude: normalized.country.coordinates.latitude,
              longitude: normalized.country.coordinates.longitude,
              mapboxId: normalized.country.mapbox_id,
            })
            .onConflictDoNothing({
              target: [regions.name, regions.type, regions.parentId],
            })
            .returning()
        : [null];

      // Upsert Region (state)
      const [region] = normalized.region
        ? await txn
            .insert(regions)
            .values({
              name: normalized.region.name,
              type: 'state',
              parentId: country?.id ?? null,
              latitude: normalized.region.coordinates.latitude,
              longitude: normalized.region.coordinates.longitude,
              mapboxId: normalized.region.mapbox_id,
            })
            .onConflictDoNothing({
              target: [regions.name, regions.type, regions.parentId],
            })
            .returning()
        : [null];

      // Upsert Location
      const [location] = await txn
        .insert(locations)
        .values({
          placeId: place_id,
          name: normalized.place?.name ?? 'Unknown',
          latitude: coords.latitude,
          longitude: coords.longitude,
          regionId: region?.id ?? country?.id ?? null,
        })
        .onConflictDoUpdate({
          target: locations.placeId,
          set: {
            name: normalized.place?.name ?? sql`${locations.name}`,
            latitude: coords.latitude,
            longitude: coords.longitude,
          },
        })
        .returning();

      return {
        locationId: location?.id ?? null,
      };
    });
  }
}
