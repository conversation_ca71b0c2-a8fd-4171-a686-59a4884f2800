import { ApiTags } from '@nestjs/swagger';
import { Controller, Get, Query } from '@nestjs/common';

// import * as schema from '@/db/schema';
import { LocationService } from './location.service';

@Controller('locations')
@ApiTags('locations')
export class LocationController {
  constructor(
    // @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly locationService: LocationService,
  ) {}

  @Get('autocomplete')
  async getLocationSuggestions(@Query('query') query: string) {
    return this.locationService.getLocationSuggestions(query);
  }
}
