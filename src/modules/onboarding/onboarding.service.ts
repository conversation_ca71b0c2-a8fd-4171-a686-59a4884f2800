import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  users,
  workspaces,
  students,
  specialists,
  workspaceDocuments,
  professionals,
  // workspaceSubscriptions,
  organisations,
} from '@/db/schema';

import { PublicProfileDto } from './dto/public-onboarding.dto';
import { AccountSetupStage, AccountType } from '@/constants/users';
import {
  BulkNetworkingDto,
  DocumentsArrayDto,
  SurgeonProfileDto,
  // extractBasicFields,
} from './dto/specialist-onboarding.dto';
import { EntityType } from '@/constants/user-types';
import { ProfessionalProfileDto } from './dto/professional-onboarding.dto';
import { StudentProfileDto } from './dto/student-onboarding.dto';
import {
  OrganisationProfileDto,
  // extractNonPrestigeFields,
} from './dto/organisation-onboarding.dto';

// import { itemNotFound } from '@/exceptions/common';

import { PrimarySpeciality, StudentStatus, WorkspacesStatus } from '@/constants/workspaces';
import {
  WorkspaceDocumentsStatus,
  WorkspaceDocumentVerificationStatus,
} from '@/constants/workspace-documents';
// import { EntityName } from '@/constants/entities';
// import { lowLevelSubscriptionPlanCantChooseBothAsPrimarySpecialist } from '@/exceptions/workspaces';
// import { SubscriptionPlan } from '@/constants/subscription-plans';
import { UserSegment } from '@/constants/user-segments';

@Injectable()
export class OnboardingService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  publicProfileSetup(
    userId: string,
    createRegisterDto: PublicProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (txn) => {
      return txn
        .update(users)
        .set({
          ...createRegisterDto,
          currentStage: AccountSetupStage.COMPLETED,
        })
        .where(and(eq(users.id, userId)))
        .returning();
    });
  }

  async specialistProfileSetup(
    userId: string,
    workspaceId: string,
    surgeonDetails: SurgeonProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (txn) => {
      const currentStage = AccountSetupStage.DOCUMENT_UPLOAD;

      const [user] = await txn
        .update(users)
        .set({
          ...surgeonDetails,
          currentStage,
        })
        .where(and(eq(users.id, userId)))
        .returning();

      await txn
        .update(workspaces)
        .set({
          primarySpeciality: surgeonDetails.speciality as unknown as PrimarySpeciality,
          userSegment: UserSegment.CARDIAC_SPECIALIST,
        })
        .where(
          and(eq(workspaces.id, workspaceId), eq(workspaces.status, WorkspacesStatus.PENDING)),
        );

      await txn.insert(specialists).values({
        ...surgeonDetails,
        workspaceId,
        status: 1,
      });

      return { providerId: user.providerId, currentStage };
    });
  }

  async professionalProfileSetup(
    userId: string,
    workspaceId: string,
    professionalDetails: ProfessionalProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (txn) => {
      const currentStage = AccountSetupStage.DOCUMENT_UPLOAD;

      const [user] = await txn
        .update(users)
        .set({
          ...professionalDetails,
          currentStage,
        })
        .where(and(eq(users.id, userId)))
        .returning({ providerId: users.providerId });

      await txn
        .update(workspaces)
        .set({
          primarySpeciality: professionalDetails.primarySpeciality as unknown as PrimarySpeciality,
        })
        .where(
          and(eq(workspaces.id, workspaceId), eq(workspaces.status, WorkspacesStatus.PENDING)),
        );

      await txn.insert(professionals).values({
        ...professionalDetails,
        workspaceId,
        status: 1,
      });

      return { providerId: user.providerId, currentStage };
    });
  }

  async organisationProfileSetup(
    userId: string,
    workspaceId: string,
    organisationDetails: OrganisationProfileDto,
    locationId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (txn) => {
      const nextStage = AccountSetupStage.DOCUMENT_UPLOAD;

      const [user] = await txn
        .update(users)
        .set({
          ...organisationDetails,
          currentStage: nextStage,
        })
        .where(and(eq(users.id, userId)))
        .returning({ providerId: users.providerId });

      await txn
        .update(workspaces)
        .set({
          primarySpeciality: organisationDetails.primarySpeciality as unknown as PrimarySpeciality,
        })
        .where(
          and(eq(workspaces.id, workspaceId), eq(workspaces.status, WorkspacesStatus.PENDING)),
        );

      await txn.insert(organisations).values({
        ...organisationDetails,
        locationId,
        workspaceId,
        status: 1,
      });

      return { providerId: user.providerId, currentStage: nextStage };
    });
  }

  async studentProfileSetup(
    userId: string,
    workspaceId: string,
    studentDetails: StudentProfileDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (txn) => {
      const nextStage = AccountSetupStage.DOCUMENT_UPLOAD;

      const [user] = await txn
        .update(users)
        .set({
          // Added accountType: "STUDENT" here
          accountType: AccountType.PUBLIC,
          currentStage: nextStage,
        })
        .where(and(eq(users.id, userId)))
        .returning({ providerId: users.providerId });

      await txn.insert(students).values({
        ...studentDetails,
        status: StudentStatus.ACTIVE,
        workspaceId,
      });

      return {
        providerId: user.providerId,
        currentStage: nextStage,
        // accountType to be set in the token
        accountType: AccountType.PUBLIC,
      };
    });
  }

  /**
   * Consolidated document upload method for all user types
   */
  async documentUpload(
    // userType: 'specialist' | 'professional' | 'student' | 'organisation',
    userId: string,
    workspaceId: string,
    documentDetails: DocumentsArrayDto['documents'],
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (trx) => {
      // Get subscription info to apply document verification rules
      // const subscriptionInfo = await this.getSubscriptionInfo(workspaceId, trx);

      // Determine verification status based on subscription
      // const verificationStatus = this.getVerificationStatusBySubscription(
      //   subscriptionInfo.title,
      //   userType,
      // );

      const documentsToAdd = documentDetails.flatMap((doc) =>
        doc.uploadDetails.map((uploadDetail) => ({
          documentTypeId: doc.documentTypeId,
          fileName: uploadDetail.fileName,
          filePath: uploadDetail.filePath,
          fileSize: uploadDetail.fileSize,
          mimeType: uploadDetail.mimeType,
          sortOrder: uploadDetail.sortOrder,
          workspaceId,
          verificationStatus: WorkspaceDocumentVerificationStatus.PENDING,
          status: WorkspaceDocumentsStatus.ACTIVE,
        })),
      );

      if (documentsToAdd.length) await trx.insert(workspaceDocuments).values(documentsToAdd);

      return trx
        .update(users)
        .set({
          currentStage: AccountSetupStage.ADDING_NETWORK,
        })
        .where(and(eq(users.id, userId)))
        .returning();
    });
  }

  async bulkNetworkAdding(
    userId: string,
    workspaceId: string,
    userIds: BulkNetworkingDto['userIds'],
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const userIdsToAdd = userIds.map((ui) => ({
      entityType: EntityType.WORKSPACE,
      entityId: workspaceId,
      workspaceId: ui,
    }));

    return dbOrTransaction.transaction(async (trx) => {
      await trx.insert(schema.workspaceFollowers).values(userIdsToAdd).returning();

      return trx
        .update(users)
        .set({
          currentStage: AccountSetupStage.COMPLETED,
        })
        .where(eq(users.id, userId))
        .returning();
    });
  }

  /**
   * Helper method to get subscription information
   */
  // private async getSubscriptionInfo(workspaceId: string, txn: PostgresJsDatabase<typeof schema>) {
  //   const today = new Date();
  //   const todayISOString = today.toISOString().split('T')[0]; // Format as YYYY-MM-DD

  //   const workspaceSubscriptionDetails = await txn.query.workspaceSubscriptions.findFirst({
  //     where: and(
  //       eq(workspaceSubscriptions.workspaceId, workspaceId),
  //       gte(workspaceSubscriptions.endDate, todayISOString),
  //     ),
  //     with: {
  //       subscription: true,
  //     },
  //   });

  //   if (!workspaceSubscriptionDetails) throw itemNotFound(EntityName.SUBSCRIPTION);

  //   return workspaceSubscriptionDetails.subscription;
  // }
}
