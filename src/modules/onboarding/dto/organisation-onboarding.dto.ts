import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { BaseProfileDto } from './base-onboarding.dto';

import { Speciality } from '@/constants/workspaces';
import { Type } from 'class-transformer';

export class CoordinatesDto {
  @ApiProperty({
    description: 'Latitude of the location (required)',
    example: 19.076,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  latitude: number;

  @ApiProperty({
    description: 'Longitude of the location (required)',
    example: 72.877,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  longitude: number;
}

// Organisation-specific DTO
export class OrganisationProfileDto extends BaseProfileDto {
  // @ApiProperty({
  //   description: 'Organization website',
  //   required: false,
  //   example: 'https://example.com',
  // })
  // @IsString()
  // @IsOptional()
  // @MinLength(MIN_LENGTH)
  // @MaxLength(MAX_LENGTH)
  // website?: string;

  // @ApiProperty({
  //   description: 'Organization size',
  //   required: false,
  //   example: '50-100',
  // })
  // @IsString()
  // @IsOptional()
  // @MaxLength(MAX_LENGTH)
  // organisationSize?: string;

  // @ApiProperty({
  //   description: 'Name of the point of contact',
  //   required: false,
  //   example: 'John Doe',
  // })
  // @IsString()
  // @IsOptional()
  // @MinLength(MIN_LENGTH)
  // @MaxLength(MAX_LENGTH)
  // pointOfContactName?: string;

  // @ApiProperty({
  //   description: 'Phone number of the point of contact',
  //   required: false,
  //   example: '+**********',
  // })
  // @IsString()
  // @IsOptional()
  // @MinLength(MIN_LENGTH)
  // @MaxLength(MAX_LENGTH)
  // pointOfContactPhone?: string;

  // @ApiProperty({
  //   description: 'wants subsidiary accounts or not',
  //   required: false,
  //   example: true,
  // })
  // @IsBoolean()
  // @IsOptional()
  // allowSubsidiaries?: boolean;

  // Organisation-specific fields
  @IsNotEmpty()
  @IsUUID()
  segmentCategoryId: string;

  @ApiProperty({
    name: 'primarySpeciality',
    enum: Speciality,
    required: false,
    default: Speciality.CARDIAC_SURGEON,
    description: "send this if it's professional account type",
  })
  @IsEnum(Speciality)
  primarySpeciality: Speciality;

  @ApiProperty({
    description: 'Location Place ID of the organisation location',
    example: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
  })
  @IsString()
  @IsNotEmpty()
  locationPlaceId: string;

  @ApiProperty({
    description: 'Full label of the organisation location',
    example: 'Mumbai, Maharashtra, India',
  })
  @IsString()
  @IsNotEmpty()
  location: string;

  @ApiProperty({
    description: 'Latitude and longitude of the organisation location',
    type: CoordinatesDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CoordinatesDto)
  locationCoordinates: CoordinatesDto;

  @IsString()
  @IsOptional()
  mapLink: string;

  @IsUUID()
  @IsOptional()
  parentOrganisationId: string;
}

// Helper function to extract only basic fields
export function extractNonPrestigeFields(
  dto: OrganisationProfileDto,
): Omit<OrganisationProfileDto, 'organisationSize' | 'wantsSubsidiaryAccounts'> {
  return {
    profileImageUrl: dto.profileImageUrl,
    profileImageUrlThumbnail: dto.profileImageUrlThumbnail,
    introductoryStatement: dto.introductoryStatement,
    segmentCategoryId: dto.segmentCategoryId,
    primarySpeciality: dto.primarySpeciality,
    locationPlaceId: dto.locationPlaceId,
    location: dto.location,
    locationCoordinates: dto.locationCoordinates,
    mapLink: dto.mapLink,
    parentOrganisationId: dto.parentOrganisationId,
  };
}
