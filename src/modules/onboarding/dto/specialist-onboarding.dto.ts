import {
  <PERSON><PERSON>rray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { BaseProfileDto } from './base-onboarding.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Speciality } from '@/constants/workspaces';

// Surgeon-specific DTO
export class SurgeonProfileDto extends BaseProfileDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  qualifications: string;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  jobTitle: string;

  @IsNotEmpty()
  @IsUUID()
  @IsOptional()
  employerId: string;

  @IsNotEmpty()
  @IsEnum(Speciality)
  speciality: Speciality;
}

// Helper function to extract only basic fields
export function extractBasicFields(
  dto: SurgeonProfileDto,
): Pick<SurgeonProfileDto, 'introductoryStatement' | 'title'> {
  return {
    introductoryStatement: dto.introductoryStatement,
    title: dto.title,
  };
}

export class DocumentDto {
  @ApiProperty({
    name: 'filePath',
    type: 'string',
    required: true,
    example: 'uploads/files/document.pdf',
  })
  @IsString()
  @IsNotEmpty()
  filePath: string;

  @ApiProperty({
    name: 'fileName',
    type: 'string',
    required: true,
    example: 'id card',
  })
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({
    name: 'fileSize',
    type: 'number',
    required: true,
    example: 3233,
    description: 'file size in bytes',
  })
  @IsNumber()
  @IsNotEmpty()
  // @Max(OpportunitiesAttachments.MAX_FILE_SIZE, { message: 'File size must not exceed 5 MB.' })
  fileSize: number;

  @ApiProperty({
    name: 'sortOrder',
    type: 'number',
    required: true,
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  sortOrder: number;

  @ApiProperty({
    name: 'mimeType',
    type: 'string',
    required: true,
    example: 'pdf',
  })
  @IsString()
  @IsNotEmpty()
  mimeType: string;
}

export class DocumentWithTypeDto {
  @ApiProperty({
    name: 'documentTypeId',
    type: 'string',
    required: true,
    example: '434234-4234234-42342343-342342',
  })
  @IsUUID()
  @IsNotEmpty()
  documentTypeId: string;

  @ApiProperty({
    name: 'uploadDetails',
    type: [DocumentDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DocumentDto)
  uploadDetails: DocumentDto[];
}

export class DocumentsArrayDto {
  @ApiProperty({
    type: [DocumentWithTypeDto],
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DocumentWithTypeDto)
  documents: DocumentWithTypeDto[];
}

export class BulkNetworkingDto {
  @ApiProperty({
    description: 'users ids for network',
    required: true,
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsUUID(4, { each: true })
  @IsNotEmpty()
  userIds: string[];
}
