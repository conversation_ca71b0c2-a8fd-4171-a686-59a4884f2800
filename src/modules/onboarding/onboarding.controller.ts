import { ApiTags } from '@nestjs/swagger';
import { Body, Controller, Inject, Post } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';
import { getAuth } from 'firebase-admin/auth';

import { User } from '@/decorators/user.decorator';
import { AccountTypes } from '@/decorators/account-types.decorator';
import { AccountSetupStages } from '@/decorators/account-setup-stage.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';

import * as schema from '@/db/schema';
import { users } from '@/db/schema';

import { UserData } from '@/interfaces/auth';

import { AuthConstants } from '@/constants/auth';
import { Role } from '@/constants/roles';
import { AccountSetupStage, AccountType } from '@/constants/users';
import { EntityName } from '@/constants/entities';

import { itemNotFound } from '@/exceptions/common';

import { PublicProfileDto } from './dto/public-onboarding.dto';
import { DocumentsArrayDto, SurgeonProfileDto } from './dto/specialist-onboarding.dto';
import { ProfessionalProfileDto } from './dto/professional-onboarding.dto';
import { StudentProfileDto } from './dto/student-onboarding.dto';
import { OrganisationProfileDto } from './dto/organisation-onboarding.dto';

import { OnboardingService } from './onboarding.service';
import { RolesService } from '@/modules/roles/roles.service';
import { UserRolesService } from '@/modules/user-roles/user-roles.service';
import { LocationService } from '@/modules/location/location.service';

@Controller('onboarding')
@ApiTags('onboarding')
export class OnboardingController {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly onboardingService: OnboardingService,
    private readonly rolesService: RolesService,
    private readonly userRolesService: UserRolesService,
    private readonly locationService: LocationService,
  ) {}

  @Post('profile-setup/public')
  @AccountTypes(AccountType.PUBLIC)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async publicProfileSetup(
    @Body() profileSetupDto: PublicProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const user = await this.onboardingService.publicProfileSetup(userId, profileSetupDto, txn);

      const role = await this.rolesService.findRoleByKey(Role.PUBLIC);
      if (!role) throw itemNotFound(EntityName.ROLE);

      // Create user role and associate it
      await this.userRolesService.createUserRole({ userId, roleId: role.id }, txn);

      const rolePermissions = await this.rolesService.findPermissionsByRoleId(role.id);
      const mappedPermissions = rolePermissions
        ? rolePermissions?.rolesPermissions.map((rp) => rp.permission.key)
        : [];

      await this.updateFirebaseClaims(user[0].providerId, {
        [AuthConstants.FIREBASE_CLAIM_ROLE]: role.key,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: mappedPermissions,
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.COMPLETED,
        [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [],
        [AuthConstants.FIREBASE_CLAIM_USER_SEGMENT]: Role.PUBLIC,
      });

      return { nextStep: AccountSetupStage.COMPLETED };
    });
  }

  @Post('profile-setup/specialist')
  // @UserRoles(Role.CARDIAC_SPECIALIST)  setting role in this api
  @AccountTypes(AccountType.PROFESSIONAL)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async specialistProfileSetup(
    @Body() profileSetupDto: SurgeonProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const user = await this.onboardingService.specialistProfileSetup(
        userId,
        workspaceId,
        profileSetupDto,
        txn,
      );

      const role = await this.rolesService.findRoleByKey(Role.CARDIAC_SPECIALIST);
      if (!role) throw itemNotFound(EntityName.ROLE);

      // Create user role and associate it
      await this.userRolesService.createUserRole({ userId, roleId: role.id, workspaceId }, txn);

      const rolePermissions = await this.rolesService.findPermissionsByRoleId(role.id);
      const mappedPermissions = rolePermissions
        ? rolePermissions?.rolesPermissions.map((rp) => rp.permission.key)
        : [];

      await this.updateFirebaseClaims(user.providerId, {
        [AuthConstants.FIREBASE_CLAIM_ROLE]: role.key,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: mappedPermissions,
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.DOCUMENT_UPLOAD,
        [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [
          AccountSetupStage.ADDING_NETWORK,
          AccountSetupStage.DOCUMENT_UPLOAD,
          AccountSetupStage.SUBSCRIPTION,
        ],
        [AuthConstants.FIREBASE_CLAIM_USER_SEGMENT]: Role.CARDIAC_SPECIALIST,
      });

      return { nextStep: AccountSetupStage.DOCUMENT_UPLOAD };
    });
  }

  @Post('profile-setup/allied-cardiac')
  @AccountTypes(AccountType.PROFESSIONAL)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async alliedCardiacProfileSetup(
    @Body() profileSetupDto: ProfessionalProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const user = await this.onboardingService.professionalProfileSetup(
        userId,
        workspaceId,
        profileSetupDto,
        txn,
      );

      const role = await this.rolesService.findRoleByKey(Role.ALLIED_CARDIAC);
      if (!role) throw itemNotFound(EntityName.ROLE);

      await this.userRolesService.createUserRole({ userId, roleId: role.id, workspaceId }, txn);

      const rolePermissions = await this.rolesService.findPermissionsByRoleId(role.id);
      const mappedPermissions = rolePermissions
        ? rolePermissions.rolesPermissions.map((rp) => rp.permission.key)
        : [];

      await this.updateFirebaseClaims(user.providerId, {
        [AuthConstants.FIREBASE_CLAIM_ROLE]: role.key,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: mappedPermissions,
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.DOCUMENT_UPLOAD,
        [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [
          AccountSetupStage.ADDING_NETWORK,
          AccountSetupStage.SUBSCRIPTION,
          AccountSetupStage.DOCUMENT_UPLOAD,
        ],
        [AuthConstants.FIREBASE_CLAIM_USER_SEGMENT]: Role.ALLIED_CARDIAC,
      });

      return { nextStep: AccountSetupStage.DOCUMENT_UPLOAD };
    });
  }

  @Post('profile-setup/student')
  // @UserRoles(Role.STUDENT)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async studentProfileSetup(
    @Body() profileSetupDto: StudentProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const user = await this.onboardingService.studentProfileSetup(
        userId,
        workspaceId,
        profileSetupDto,
        txn,
      );

      const role = await this.rolesService.findRoleByKey(Role.STUDENT);
      if (!role) throw itemNotFound(EntityName.ROLE);

      await this.userRolesService.createUserRole({ userId, roleId: role.id, workspaceId }, txn);

      await this.updateFirebaseClaims(user.providerId, {
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: user.currentStage,
        [AuthConstants.FIREBASE_CLAIM_USER_SEGMENT]: Role.STUDENT,
      });

      return user;
    });
  }

  @Post('profile-setup/organisation')
  // @UserRoles(Role.ORGANISATION)
  @AccountTypes(AccountType.ORGANISATION)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async organisationProfileSetup(
    @Body() profileSetupDto: OrganisationProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      // Getting locationId and location to add in org profile, which we can use directly to show the users location if we want
      const { locationId } = (await this.locationService.saveLocation(
        profileSetupDto.locationCoordinates,
        profileSetupDto.locationPlaceId,
        txn,
      )) as {
        locationId: string;
      };

      const user = await this.onboardingService.organisationProfileSetup(
        userId,
        workspaceId,
        profileSetupDto,
        locationId,
        txn,
      );

      const role = await this.rolesService.findRoleByKey(Role.ORGANISATION);
      if (!role) throw itemNotFound(EntityName.ROLE);

      await this.userRolesService.createUserRole({ userId, roleId: role.id, workspaceId }, txn);

      const rolePermissions = await this.rolesService.findPermissionsByRoleId(role.id);
      const mappedPermissions = rolePermissions
        ? rolePermissions.rolesPermissions.map((rp) => rp.permission.key)
        : [];

      await this.updateFirebaseClaims(user.providerId, {
        [AuthConstants.FIREBASE_CLAIM_ROLE]: role.key,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: mappedPermissions,
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.DOCUMENT_UPLOAD,
        [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [
          AccountSetupStage.ADDING_NETWORK,
          AccountSetupStage.DOCUMENT_UPLOAD,
          AccountSetupStage.SUBSCRIPTION,
        ],
        [AuthConstants.FIREBASE_CLAIM_USER_SEGMENT]: Role.ORGANISATION,
      });

      return { nextStep: AccountSetupStage.DOCUMENT_UPLOAD };
    });
  }

  /**
   * Unified document upload endpoint for all user types
   */
  @Post('document-upload')
  @UserRoles(Role.CARDIAC_SPECIALIST, Role.ALLIED_CARDIAC, Role.ORGANISATION, Role.STUDENT)
  @AccountSetupStages(AccountSetupStage.DOCUMENT_UPLOAD)
  async documentUploads(
    @Body() documentsDto: DocumentsArrayDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const userResult = await this.onboardingService.documentUpload(
      userId,
      workspaceId,
      documentsDto.documents,
    );

    const { providerId } = userResult[0];
    await this.updateFirebaseClaims(providerId, {
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.ADDING_NETWORK,
      [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [
        AccountSetupStage.ADDING_NETWORK,
        AccountSetupStage.SUBSCRIPTION,
      ],
    });

    return userResult;
  }

  @Post('complete-networking-stage')
  @UserRoles(Role.CARDIAC_SPECIALIST, Role.ALLIED_CARDIAC, Role.ORGANISATION, Role.STUDENT)
  @AccountSetupStages(AccountSetupStage.ADDING_NETWORK)
  async completeNetworkingStage(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_UID) providerId: string,
  ) {
    await this.drizzleDev
      .update(users)
      .set({
        currentStage: AccountSetupStage.COMPLETED,
      })
      .where(and(eq(users.id, userId)));

    await this.updateFirebaseClaims(providerId, {
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.COMPLETED,
      [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [AccountSetupStage.SUBSCRIPTION],
    });

    return 'ok';
  }

  /**
   * Helper method to update Firebase custom claims
   */
  private async updateFirebaseClaims(providerId: string, newClaims: Record<string, any>) {
    const userRecord = await getAuth().getUser(providerId);
    const currentClaims: UserData = userRecord.customClaims as UserData;

    // Merge the new claims with the existing ones
    const updatedClaims = {
      ...currentClaims,
      ...newClaims,
    };

    // Update Firebase custom claims
    await getAuth().setCustomUserClaims(providerId, updatedClaims);
  }
}
