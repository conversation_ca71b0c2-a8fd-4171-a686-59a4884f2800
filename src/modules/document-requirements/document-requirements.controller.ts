import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { DocumentRequirementsService } from './document-requirements.service';

import { AccountSetupStage, AccountType } from '@/constants/users';

import { AccountSetupStages } from '@/decorators/account-setup-stage.decorator';
import { GetDocumentRequirementsDto } from './dto/get-document-requirements.dto';
import { AccountTypes } from '@/decorators/account-types.decorator';

@ApiTags('document-requirements')
@Controller('document-requirements')
export class DocumentRequirementsController {
  constructor(private readonly documentRequirementsService: DocumentRequirementsService) {}

  @AccountTypes(AccountType.PROFESSIONAL, AccountType.ORGANISATION)
  @Get('user-segment/:userSegment')
  @AccountSetupStages(AccountSetupStage.DOCUMENT_UPLOAD)
  async getDocumentRequirementsByUserSegment(@Param() params: GetDocumentRequirementsDto) {
    return this.documentRequirementsService.getDocumentRequirementsByUserSegment(
      params.userSegment,
    );
  }
}
