import {
  IsBoolean,
  <PERSON><PERSON>otE<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsString,
  <PERSON>U<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ValidateIf,
} from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

import { CommonPostCreateDto } from '@/modules/posts/dto/create-post.dto';
import { MAX_LENGTH, MIN_LENGTH } from '@/constants/posts';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTextPostDto extends CommonPostCreateDto {
  @ApiProperty({
    name: 'content',
    type: 'string',
    required: false,
    description:
      'Content of the post. Optional **only** if repostOfPostId is provided; otherwise required.',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @ValidateIf((o) => !o.repostOfPostId || o.content !== undefined)
  @IsNotEmpty()
  @IsString()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  content?: string;

  @ApiProperty({
    name: 'repostOfPostId',
    type: 'string',
    required: false,
    description: 'Optional. If this is a quote post, provide the original post ID being quoted.',
  })
  @IsUUID()
  @IsOptional()
  repostOfPostId?: string;

  @ApiProperty({
    name: 'includeQuote',
    type: 'boolean',
    required: false,
    description: 'Optional. If this is a quote post, provide the text being quoted.',
  })
  @IsBoolean()
  @IsOptional()
  includeQuote?: boolean;
}
