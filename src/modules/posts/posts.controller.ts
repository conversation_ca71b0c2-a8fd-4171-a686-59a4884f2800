import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  ParseUUI<PERSON>ipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { ApiTags } from '@nestjs/swagger';

import * as schema from '@/db/schema';
import { articlePosts } from '@/db/schema';

import { User } from '@/decorators/user.decorator';
import { ActiveWorkspace } from '@/decorators/active-workspace.decorator';
import { Public } from '@/decorators/public.decorator';

import {
  commentAlreadyPinned,
  commentNotAQuestionPost,
  pinnedCommentNotFound,
} from '@/exceptions/posts';
import { unauthorized } from '@/exceptions/system';
import { itemNotFound } from '@/exceptions/common';

import { CreateMediaPostDto } from './media-posts/dto/create-media-post.dto';
import { CreateArticlePostDto } from './article-posts/dto/create-article-post.dto';
import { CreateQuestionPostDto } from './question-posts/dto/create-question-post.dto';
import { UpdateTextPostDto } from './text-posts/dto/update-text-post.dto';
import { UpdateQuestionPostDto } from './question-posts/dto/update-question-post.dto';
import { UpdateArticlePostDto } from './article-posts/dto/update-article-post.dto';
import { UpdateMediaPostDto } from './media-posts/dto/update-media-post.dto';
import { CreateCommentDto } from './post-comments/dto/create-post-comment';
import { UpdateCommentDto } from './post-comments/dto/update-post-comment';
import { CreateTextPostDto } from './text-posts/dto/create-text-post.dto';
import { FeedFetchPostDto, TagFeedFetchPostDto } from './dto/get-post.dto';
import { CommentFetchDto } from './post-comments/dto/get-post-comments.dto';
import { CreatePollPostDto } from './poll-posts/dto/create-poll-post.dto';
import { VotePollDto } from './poll-posts/dto/vote-poll.dto';
import { UpdatePollPostDto } from './poll-posts/dto/update-poll-post.dto';
import { CreateLinkPostDto } from './link-posts/dto/create-link-post.dto';
import { UpdateLinkPostDto } from './link-posts/dto/update-link-post.dto';

import { extractImagePathsFromRichText } from '@/utils/rich-text';
import { guessMediaTypeFromPath } from '@/utils/files';
import { sanitizeContent } from '@/utils/sanitize';

import { AuthConstants } from '@/constants/auth';
import { PostType } from '@/constants/posts';
import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';
import { ENABLE_PUBLIC_FEED_ACCESS } from '@/constants/system';

import { WorkspacesService } from '@/modules/workspaces/workspaces.service';
import { UsersService } from '@/modules/users/users.service';
import { PostsService } from './posts.service';
import { ArticlePostsService } from './article-posts/article-posts.service';
import { PostMediasService } from './media/post-medias.service';
import { QuestionPostsService } from './question-posts/question-posts.service';
import { PostLikesService } from './post-likes/post-likes.service';
import { PostCommentsService } from './post-comments/post-comments.service';
import { PostCommentLikesService } from './post-comment-likes/post-comment-likes.service';
import { PollPostsService } from './poll-posts/poll-posts.service';
import { LinkPostsService } from './link-posts/link-posts.service';

@ApiTags('posts')
@Controller('posts')
export class PostsController {
  constructor(
    private readonly postsService: PostsService,
    private readonly articlePostsService: ArticlePostsService,
    private readonly workspacesService: WorkspacesService,
    private readonly postMediasService: PostMediasService,
    private readonly questionPostsService: QuestionPostsService,
    private readonly pollPostsService: PollPostsService,
    private readonly linkPostsService: LinkPostsService,
    private readonly usersService: UsersService,
    private readonly postLikesService: PostLikesService,
    private readonly postCommentsService: PostCommentsService,
    private readonly postCommentLikesService: PostCommentLikesService,
    @Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>,
  ) {}

  @Get('feed')
  @Public()
  async findAll(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Query() query: FeedFetchPostDto,
  ) {
    if (!ENABLE_PUBLIC_FEED_ACCESS && !userId) {
      throw unauthorized();
    }

    const { limit, offset, searchKeyword, postTypes } = query;

    const response = await this.postsService.findAll({
      limit,
      offset,
      searchKeyword,
      postTypes,
      entityId: workspaceId ?? userId,
    });

    return response;
  }

  @Get('feed/:postId')
  @Public()
  async getSinglePost(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    if (!ENABLE_PUBLIC_FEED_ACCESS && !(workspaceId || userId)) {
      throw unauthorized();
    }

    return this.postsService.findOne({ postId, entityId: workspaceId ?? userId });
  }

  @Get('feed/unpublished/:postId')
  async getSingleScheduledPost(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.postsService.findOneScheduledOrDraftPost({
      postId,
      entityId: workspaceId ?? userId,
      entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
    });
  }

  @Get('tag/:tagName')
  @Public()
  async findByTag(
    @Param('tagName') tagName: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string,
    @Query() query: TagFeedFetchPostDto,
  ) {
    if (!ENABLE_PUBLIC_FEED_ACCESS && !userId) {
      throw unauthorized();
    }

    const { limit, offset, searchKeyword, postTypes } = query;

    const response = await this.postsService.findAllByTag({
      tagName,
      limit,
      offset,
      searchKeyword,
      postTypes,
      entityId: workspaceId ?? userId,
    });

    return response;
  }

  @Post('text')
  // @ActiveWorkspace() - Commented out to allow public posts
  async createTextPost(
    @Body() createTextPostDto: CreateTextPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    // Sanitize the text content to prevent XSS attacks
    const textContent = sanitizeContent(createTextPostDto?.content || '');

    const getAdditionalTextRepostsDetails =
      await this.postsService.getAdditionalTextRepostsDetails(createTextPostDto);

    return this.postsService.createPostWithTransaction(
      { ...createTextPostDto, content: textContent, ...getAdditionalTextRepostsDetails },
      publisherId,
      workspaceId,
      PostType.TEXT,
    );
  }

  @Post('media')
  // @ActiveWorkspace() - Commented out to allow public posts
  async createMediaPost(
    @Body() createMediaPostDto: CreateMediaPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    return this.postsService.createPostWithTransaction(
      createMediaPostDto,
      publisherId,
      workspaceId,
      PostType.MEDIA,
      async (postId, tx) => {
        // Associate medias with the post
        await this.postMediasService.createMultiplePostMedia(postId, createMediaPostDto.medias, tx);
      },
    );
  }

  @Post('article')
  // @ActiveWorkspace() - Commented out to allow public posts
  async createArticlePost(
    @Body() createArticlePostDto: CreateArticlePostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    const { title, coverImagePath } = createArticlePostDto;
    // Sanitize the article content to prevent XSS attacks
    const articleContent = sanitizeContent(createArticlePostDto.body);

    // Extract image URLs from rich text content
    const imagePathsFromContent = extractImagePathsFromRichText(articleContent);

    return this.postsService.createPostWithTransaction(
      { ...createArticlePostDto, body: articleContent },
      publisherId,
      workspaceId,
      PostType.ARTICLE,
      async (postId, tx) => {
        // Create the article post
        await this.articlePostsService.createArticlePost(
          {
            title,
            body: articleContent,
            coverImagePath,
            postId,
          },
          tx,
        );

        // Store all embedded images as post media for tracking
        if (imagePathsFromContent.length > 0) {
          const mediaItems = imagePathsFromContent.map((path) => ({
            mediaPath: path,
            mediaType: guessMediaTypeFromPath(path),
          }));

          await this.postMediasService.createMultiplePostMedia(postId, mediaItems, tx);
        }

        // If there's a cover image, store it as post media too
        if (coverImagePath) {
          await this.postMediasService.createPostMedia(
            postId,
            {
              mediaPath: coverImagePath,
              mediaType: guessMediaTypeFromPath(coverImagePath),
              altText: `${title} cover image`,
            },
            tx,
            true, // to track it as cover pic
          );
        }
      },
    );
  }

  @Post('question')
  // @ActiveWorkspace() - Commented out to allow public posts
  async createQuestionPost(
    @Body() createQuestionPostDto: CreateQuestionPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    // Sanitize the question content to prevent XSS attacks
    const questionContent = sanitizeContent(createQuestionPostDto.content);

    return this.postsService.createPostWithTransaction(
      { ...createQuestionPostDto, content: questionContent },
      publisherId,
      workspaceId,
      PostType.QUESTION,
      async (postId, tx) => {
        // Create the question post
        await this.questionPostsService.createQuestionPost(
          {
            postId,
          },
          tx,
        );
      },
    );
  }

  @Post('poll')
  // @ActiveWorkspace() - Commented out to allow public posts
  async createPollPost(
    @Body() createPollPostDto: CreatePollPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    const { question, durationDays, allowCustomAnswer, options, postScheduleDate } =
      createPollPostDto;

    return this.postsService.createPostWithTransaction(
      createPollPostDto,
      publisherId,
      workspaceId,
      PostType.POLL,
      async (postId, tx) => {
        // Create the poll post and options
        await this.pollPostsService.createPollPost(
          {
            postId,
            question,
            durationDays,
            allowCustomAnswer,
            options,
            postScheduleDate,
          },
          tx,
        );
      },
    );
  }

  @Post('link')
  async createLinkPost(
    @Body() createLinkPostDto: CreateLinkPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    const { link } = createLinkPostDto;

    if (!publisherId) {
      throw unauthorized();
    }

    // Ensure any caption is stored in the posts.content field
    const postData = {
      ...createLinkPostDto,
      // If content is not provided, it will be null which is fine
      content: createLinkPostDto.content || null,
    };

    return this.postsService.createPostWithTransaction(
      postData,
      publisherId,
      workspaceId,
      PostType.LINK,
      async (postId, tx) => {
        await this.linkPostsService.createLinkPost(
          {
            postId,
            link,
          },
          tx,
        );
      },
    );
  }

  @Delete(':postId')
  async deletePost(
    @Param('postId', new ParseUUIDPipe({ version: '4' })) postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string | null,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.postsService.softDeletePost(postId, workspaceId || null, userId);
  }

  @Patch('text/:postId')
  // @ActiveWorkspace() - Commented out to allow public post updates
  async updateTextPost(
    @Param('postId') postId: string,
    @Body() updateTextPostDto: UpdateTextPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    const { content } = updateTextPostDto;

    if (content) {
      // Sanitize the content if it's being updated
      updateTextPostDto.content = sanitizeContent(content);
    }

    // Start transaction
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.postsService.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        updateTextPostDto,
        tx,
      );

      return updatedPostBase;
    });
  }

  @Patch('question/:postId')
  // @ActiveWorkspace() - Commented out to allow public post updates
  async updateQuestionPost(
    @Param('postId') postId: string,
    @Body() updateQuestionPost: UpdateQuestionPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    const { content } = updateQuestionPost;

    if (content) {
      // Sanitize the content if it's being updated
      updateQuestionPost.content = sanitizeContent(content);
    }

    // Start transaction
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.postsService.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        updateQuestionPost,
        tx,
      );

      /* 
    we can enable this part by adding additional field, if we wanted to use this to pin a comment
    
    // Get the keys from UpdateTextPostDto to check for text post specific updates
    const questionPostFields = Object.keys(updateQuestionPost).filter(
      (key) => key in questionPosts,
    );

    if (questionPostFields.length > 0) {
      await this.questionPostsService.updateQuestionPost(postId, updateQuestionPost, tx);
    }
    */

      return updatedPostBase;
    });
  }

  @Patch('polls/:postId')
  // @ActiveWorkspace()
  @Public()
  async updatePollPost(
    @Param('postId', new ParseUUIDPipe({ version: '4' })) postId: string,
    @Body() updatePollDto: UpdatePollPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.postsService.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        updatePollDto,
        tx,
      );

      const updatePollSpecificData = {
        allowCustomAnswer: updatePollDto.allowCustomAnswer,
        durationDays: updatePollDto.durationDays,
        options: updatePollDto.options,
        question: updatePollDto.question,
        postScheduleDate: updatePollDto.postScheduleDate,
      };

      // Filter out undefined/null values to only update what's provided
      const filteredPollData = Object.fromEntries(
        Object.entries(updatePollSpecificData).filter(
          ([, value]) => value !== undefined && value !== null,
        ),
      ) as Partial<typeof updatePollSpecificData>;

      // Check if there's any poll-specific data to update
      const shouldUpdatePollPost = Object.keys(filteredPollData).length > 0;

      if (shouldUpdatePollPost) {
        await this.pollPostsService.updatePollPost(postId, filteredPollData, tx);
      }

      return updatedPostBase;
    });
  }

  @Patch('link/:postId')
  // @ActiveWorkspace() - Commented out to allow public post updates
  async updateLinkPost(
    @Param('postId', new ParseUUIDPipe({ version: '4' })) postId: string,
    @Body() updateLinkPostDto: UpdateLinkPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    const postData = {
      ...updateLinkPostDto,

      content: updateLinkPostDto.content !== undefined ? updateLinkPostDto.content : undefined,
    };

    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.postsService.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        postData,
        tx,
      );

      // Only update the link if it's provided in the DTO
      if (updateLinkPostDto.link !== undefined) {
        await this.linkPostsService.updateLinkPost(postId, { link: updateLinkPostDto.link }, tx);
      }

      return updatedPostBase;
    });
  }

  @Patch('article/:postId')
  // @ActiveWorkspace() - Commented out to allow public post updates
  async updateArticlePost(
    @Param('postId') postId: string,
    @Body() updateArticlePost: UpdateArticlePostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    const { postStatus, postScheduleDate, content, audience, community, tags } = updateArticlePost;

    // Start transaction
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.postsService.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        { postStatus, postScheduleDate, audience, community, tags, content },
        tx,
      );

      // Get the keys from UpdateArticlePostDto to check for article post specific updates
      const articlePostFields = Object.keys(updateArticlePost).filter((key) => key in articlePosts);

      if (articlePostFields.length > 0) {
        const { body, coverImagePath } = updateArticlePost;

        if (body) {
          const articleContent = sanitizeContent(body);

          const imagePathsFromContent = extractImagePathsFromRichText(articleContent);

          // Store all embedded images as post media for tracking
          const mediaItems = imagePathsFromContent.map((path) => ({
            mediaPath: path,
            mediaType: guessMediaTypeFromPath(path),
          }));

          await this.postMediasService.updateMultiplePostMedias(postId, mediaItems, tx);
        }

        if (coverImagePath) {
          const articlePostDetails = await this.articlePostsService.findAriticlePostById(postId);

          // only do operation if it's a new cover image
          if (articlePostDetails?.coverImagePath !== coverImagePath) {
            // there may have chance to not have any cover image previously. so only delete if existed before.
            if (articlePostDetails?.coverImagePath)
              await this.postMediasService.deletePostMedia(articlePostDetails.coverImagePath);

            await this.postMediasService.createPostMedia(
              postId,
              {
                mediaPath: coverImagePath,
                mediaType: guessMediaTypeFromPath(coverImagePath),
                altText: `${articlePostDetails?.title} cover image`,
              },
              tx,
              true, // to track it as cover pic
            );
          }
        }

        await this.articlePostsService.updateArticlePost(postId, updateArticlePost, tx);
      }

      return updatedPostBase;
    });
  }

  @Patch('media/:postId')
  // @ActiveWorkspace() - Commented out to allow public post updates
  async updateMediaPost(
    @Param('postId') postId: string,
    @Body() updateMediaPost: UpdateMediaPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId?: string,
  ) {
    // Start transaction
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.postsService.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        updateMediaPost,
        tx,
      );

      // Update medias if provided
      if (updateMediaPost.medias) {
        await this.postMediasService.updateMultiplePostMedias(postId, updateMediaPost.medias, tx);
      }

      return updatedPostBase;
    });
  }

  @Get('drafts')
  // @ActiveWorkspace() - Commented out to allow public access
  async getDraftPosts(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string,
  ) {
    return this.postsService.getDraftPosts(workspaceId);
  }

  @Get('scheduled')
  // @ActiveWorkspace() - Commented out to allow public access
  async getScheduledPosts(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string,
  ) {
    return this.postsService.getScheduledPosts(workspaceId);
  }

  @Post('polls/:postId/vote')
  async votePoll(
    @Param('postId', new ParseUUIDPipe({ version: '4' })) postId: string,
    @Body() votePollDto: VotePollDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    // Checking its an active post, if not, this service fn take care of throwing an error
    await this.pollPostsService.getPollPostById(postId);

    const entityId = workspaceId ?? userId;
    const entityType = workspaceId ? EntityType.WORKSPACE : EntityType.USER;

    return this.pollPostsService.votePoll(postId, entityId, entityType, votePollDto);
  }

  @Delete('polls/:pollId/vote')
  async retractVote(
    @Param('pollId', new ParseUUIDPipe({ version: '4' })) pollId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.pollPostsService.retractVote(pollId, workspaceId ?? userId);
  }

  // POST LIKES 👍
  @Post('likes/:postId')
  @ActiveWorkspace()
  async createPostLike(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const isExist = await (workspaceId
      ? this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true)
      : this.usersService.findUserById(userId));

    if (!isExist) itemNotFound(workspaceId ? EntityName.WORKSPACE : EntityName.USER);

    return this.db.transaction(async (txn) => {
      const likeData = await this.postLikesService.createPostLike(
        postId,
        workspaceId ?? userId,
        workspaceId ? EntityType.WORKSPACE : EntityType.USER,
        txn,
      );

      // const postDetails = await this.postsService.getPostById(postId);

      // TODO: NOTIFICATION CAN BE HANDLED LATER
      // if (postDetails.workspaceId !== workspaceId) {
      //   await this.notificationService.createLikeNotification(
      //     {
      //       entityId: workspaceId ?? userId,
      //       entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
      //       postId,
      //       type: PostNotificationsType.LIKE,
      //     },
      //     txn,
      //   );
      // }

      return likeData;
    });
  }

  @Delete('likes/:postId')
  @ActiveWorkspace()
  softDeletePostLike(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.db.transaction(async (txn) => {
      const likeData = await this.postLikesService.softDeletePostLike(
        postId,
        workspaceId ?? userId,
        txn,
      );

      // TODO: NOTIFICATION CAN BE HANDLED LATER
      // await this.notificationService.invalidatePostNotification(
      //   {
      //     entityId: workspaceId ?? userId,
      //     postId: likeData.postId,
      //     type: PostNotificationsType.LIKE,
      //   },
      //   txn,
      // );

      return likeData;
    });
  }

  // Post Comments 💬
  @Post('comments/:postId')
  @ActiveWorkspace()
  createPostComment(
    @Param('postId', new ParseUUIDPipe({ version: '4' })) postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() createPostCommentDto: CreateCommentDto,
  ) {
    return this.db.transaction(async (txn) => {
      const isExist = await (workspaceId
        ? this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true)
        : this.usersService.findUserById(userId));

      if (!isExist) itemNotFound(workspaceId ? EntityName.WORKSPACE : EntityName.USER);

      const commentData = await this.postCommentsService.createPostComment(
        postId,
        workspaceId ?? userId,
        workspaceId ? EntityType.WORKSPACE : EntityType.USER,
        createPostCommentDto,
        txn,
      );

      /*    
      TODO: NOTIFICATION CAN BE HANDLED LATER

      const postDetails = await this.postsService.getPostById(postId);

      if (postDetails.workspaceId !== workspaceId) {
        await this.notificationService.createCommentNotification(
          {
            entityId: workspaceId ?? userId,
            entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
            postId,
            type: PostNotificationsType.COMMENT,
            commentId: commentData.id,
          },
          txn,
        );
      }  */

      return commentData;
    });
  }

  @Get('comments/:postId')
  async getPostComments(
    @Param('postId', new ParseUUIDPipe({ version: '4' })) postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Query() query: CommentFetchDto,
  ) {
    // checking post exist, and if not, this fn will take care of throwing the error
    await this.postsService.getPostById(postId);

    const { limit, offset, parentCommentId } = query;

    return this.postCommentsService.getComments({
      postId,
      limit,
      offset,
      parentCommentId,
      entityId: workspaceId || userId,
      entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
    });
  }

  @Patch('comments/:commentId')
  @ActiveWorkspace()
  updatePostComment(
    @Param('commentId') commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() updatePostCommentDto: UpdateCommentDto,
  ) {
    return this.postCommentsService.updatePostComment(
      commentId,
      workspaceId ?? userId,
      updatePostCommentDto,
    );
  }

  @Delete('comments/:commentId')
  @ActiveWorkspace()
  softDeletePostComment(
    @Param('commentId') commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.db.transaction(async (txn) => {
      const commentData = await this.postCommentsService.softDeletePostComment(
        commentId,
        workspaceId ?? userId,
        txn,
      );

      /* 
      TODO: NOTIFICATION CAN BE HANDLED LATER
      await this.notificationService.invalidatePostNotification(
        {
          entityId: workspaceId ?? userId,
          postId: commentData.postId,
          type: PostNotificationsType.COMMENT,
          commentId: commentData.id,
        },
        txn,
      ); */

      return commentData;
    });
  }

  // pin a comment for question post
  @Patch('question/pin-comment/:commentId')
  @ActiveWorkspace()
  async pinCommentForQuestionPost(
    @Param('commentId', new ParseUUIDPipe({ version: '4' })) commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const commentPostDetails = await this.postCommentsService.getCommentWithPostDetails(commentId);

    if (!commentPostDetails) throw itemNotFound(EntityName.POST);

    const { postId, postType, workspaceId: commentWorkspaceId } = commentPostDetails;

    // check if the workspace is the owner of the post
    const isAllowedToPinComment = commentWorkspaceId === workspaceId;
    if (!isAllowedToPinComment) throw unauthorized();

    // check if the post is a question post
    if (postType !== PostType.QUESTION) throw commentNotAQuestionPost();

    // check if the comment is already pinned
    if (commentPostDetails.pinnedCommentId === commentId) throw commentAlreadyPinned();

    await this.questionPostsService.updateQuestionPost(postId, { pinnedCommentId: commentId });
  }

  // unpin a comment for question post
  @Delete('question/pin-comment/:postId')
  @ActiveWorkspace()
  async unpinCommentForQuestionPost(
    @Param('postId', new ParseUUIDPipe({ version: '4' })) postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const questionPostDetails = await this.questionPostsService.getQuestionPostById(postId);

    if (!questionPostDetails) throw itemNotFound(EntityName.POST);

    if (!questionPostDetails.pinnedCommentId) throw pinnedCommentNotFound();

    const {
      post: { workspaceId: postWorkspaceId },
    } = questionPostDetails;

    const isAllowedToUnpinComment = postWorkspaceId === workspaceId;
    if (!isAllowedToUnpinComment) throw unauthorized();

    await this.questionPostsService.updateQuestionPost(postId, { pinnedCommentId: null });
  }

  // comment likes
  @Post(':commentId/like')
  async likeComment(
    @Param('commentId', new ParseUUIDPipe({ version: '4' })) commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const entityType = workspaceId ? EntityType.WORKSPACE : EntityType.USER;
    const entityId = workspaceId ?? userId;
    return this.postCommentLikesService.likeComment(commentId, entityId, entityType);
  }

  @Delete(':commentId/unlike')
  async unlikeComment(
    @Param('commentId', new ParseUUIDPipe({ version: '4' })) commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const entityId = workspaceId ?? userId;
    return this.postCommentLikesService.unlikeComment(commentId, entityId);
  }
}
