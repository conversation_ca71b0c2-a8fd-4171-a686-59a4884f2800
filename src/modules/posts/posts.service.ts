import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, count, eq, exists, gte, ilike, inArray, lte, ne, or, sql } from 'drizzle-orm';
import { desc, asc, isNotNull, isNull } from 'drizzle-orm/expressions';
import { alias } from 'drizzle-orm/pg-core';

import * as schema from '@/db/schema';
import {
  posts,
  NewPost,
  tags as dbTags,
  postTags,
  workspaces,
  users,
  postLikes,
  postMedias,
  postComments,
  questionPosts,
  QuotedCaption,
  pollOptions,
} from '@/db/schema';
import { postCommentLikes } from '@/db/schema/post-comment-likes';

import { countRelation, isLikedByEntity } from '@/utils/database';

import { CommonPostCreateDto } from './dto/create-post.dto';
import { UpdateCommonPostCreateDto } from './dto/update-post.dto';

import { EntityName } from '@/constants/entities';
import { PostTagsStatus } from '@/constants/post-tags';
import { PrimarySpeciality } from '@/constants/workspaces';
import {
  CommunityType,
  DEFAULT_PUBLIC_POST_TAG,
  PollPostOptionsStatus,
  PostActiveStatus,
  PostStatus,
  PostType,
  PostTypeJoinFields,
} from '@/constants/posts';
import { PostLikesStatus } from '@/constants/post-likes';
import { PostCommentsStatus } from '@/constants/post-comments';
import { PostMediaStatus } from '@/constants/post-media';
import { EntityType } from '@/constants/user-types';
import { PostCommentLikesStatus } from '@/constants/post-comment-likes';

import {
  audienceIsRequiredForWorkspacePost,
  audiencePermissionError,
  communityIsRequiredForWorkspacePost,
  invalidPostStatusTransitionException,
  postScheduleDateMinimum10MinutesRequired,
  publicPostCannotHaveAudience,
  publicPostMustBePublicCommunity,
  publicPostMustHaveSingleTag,
} from '@/exceptions/posts';
import { itemNotFound } from '@/exceptions/common';
import { unauthorized } from '@/exceptions/system';

import { CreatorDetails } from '@/interfaces/post';

import { PollPostsService } from './poll-posts/poll-posts.service';
import { PostTagsService } from './post-tags/post-tags.service';
import { TagsService } from '../tags/tags.service';
import { WorkspacesService } from '../workspaces/workspaces.service';
import { CreateTextPostDto } from './text-posts/dto/create-text-post.dto';

@Injectable()
export class PostsService {
  constructor(
    @Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>,
    private readonly pollPostsService: PollPostsService,
    private readonly tagsService: TagsService,
    private readonly postTagsService: PostTagsService,
    private readonly workspacesService: WorkspacesService,
  ) {}

  /**
   * Get the most relevant comment for a question post based on the following criteria:
   * 1. If there's a pinned comment, return it
   * 2. If no pinned comment, return the comment with the most likes
   * 3. If multiple comments have the same number of likes, return the one that reached that count first
   * 4. If no likes, return the most recent comment
   * 5. If no comments, return null
   */
  /**
   * Batch fetch featured comments for multiple question posts
   */
  private async batchGetFeaturedComments(postIds: string[]): Promise<Map<string, any>> {
    // First, get all pinned comments
    const pinnedCommentsMap = await this.batchGetPinnedComments(postIds);

    // Get post IDs that don't have pinned comments
    const postsWithoutPinnedComments = postIds.filter((id) => !pinnedCommentsMap.has(id));

    // Get most relevant comments for posts without pinned comments
    const relevantCommentsMap =
      postsWithoutPinnedComments.length > 0
        ? await this.batchGetMostRelevantComments(postsWithoutPinnedComments)
        : new Map();

    // Merge the results
    return new Map([...pinnedCommentsMap, ...relevantCommentsMap]);
  }

  /**
   * Batch fetch pinned comments for multiple posts
   */
  private async batchGetPinnedComments(postIds: string[]): Promise<Map<string, any>> {
    const pinnedCommentsData = await this.db
      .select({
        postId: questionPosts.postId,
        commentId: postComments.id,
        commentCreatorId: postComments.entityId,
        commentCreatorEntity: postComments.entityType,
        comment: postComments.comment,
        commentCreatedAt: postComments.createdAt,
      })
      .from(questionPosts)
      .innerJoin(
        postComments,
        and(
          eq(questionPosts.pinnedCommentId, postComments.id),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
        ),
      )
      .where(and(inArray(questionPosts.postId, postIds), isNotNull(questionPosts.pinnedCommentId)));

    // Get all unique creator IDs for batch creator lookup
    const creatorIds = pinnedCommentsData.map((comment) => ({
      id: comment.commentCreatorId,
      entityType: comment.commentCreatorEntity,
    }));

    const creatorsMap = await this.batchGetCommentCreators(creatorIds);

    const pinnedCommentsMap = new Map();

    for (const commentData of pinnedCommentsData) {
      const creatorKey = `${commentData.commentCreatorEntity}:${commentData.commentCreatorId}`;
      const creator = creatorsMap.get(creatorKey);

      if (creator) {
        pinnedCommentsMap.set(commentData.postId, {
          id: commentData.commentId,
          content: commentData.comment,
          creator,
          isPinned: true,
          createdAt: commentData.commentCreatedAt,
        });
      }
    }

    return pinnedCommentsMap;
  }

  /**
   * Batch fetch most relevant comments for posts without pinned comments
   */
  private async batchGetMostRelevantComments(postIds: string[]): Promise<Map<string, any>> {
    const commentsWithLikes = await this.db
      .select({
        postId: postComments.postId,
        commentId: postComments.id,
        comment: postComments.comment,
        createdAt: postComments.createdAt,
        commentCreatorId: postComments.entityId,
        commentCreatorEntity: postComments.entityType,
        likesCount: sql<number>`coalesce(count(${postCommentLikes.postCommentId}), 0)::int`,
        // Use the latest updatedAt (or createdAt if no updates) of active likes to determine when the comment reached its current like count
        latestLikeTime: sql<Date>`coalesce(max(coalesce(${postCommentLikes.updatedAt}, ${postCommentLikes.createdAt})), ${postComments.createdAt})`,
      })
      .from(postComments)
      .leftJoin(
        postCommentLikes,
        and(
          eq(postCommentLikes.postCommentId, postComments.id),
          eq(postCommentLikes.status, PostCommentLikesStatus.ACTIVE),
        ),
      )
      .where(
        and(
          inArray(postComments.postId, postIds),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
        ),
      )
      .groupBy(postComments.id, postComments.postId)
      .orderBy(
        asc(postComments.postId), // Group by post
        desc(sql`coalesce(count(${postCommentLikes.postCommentId}), 0)`), // Most likes first
        asc(
          sql`coalesce(max(coalesce(${postCommentLikes.updatedAt}, ${postCommentLikes.createdAt})), ${postComments.createdAt})`,
        ), // Earliest to reach current like count
      );

    // Group comments by post ID and select most relevant for each
    const commentsByPost = new Map<string, typeof commentsWithLikes>();

    for (const comment of commentsWithLikes) {
      if (!commentsByPost.has(comment.postId)) {
        commentsByPost.set(comment.postId, []);
      }
      commentsByPost.get(comment.postId)!.push(comment);
    }

    // Get all unique creator IDs for batch creator lookup
    const creatorIds = commentsWithLikes.map((comment) => ({
      id: comment.commentCreatorId,
      entityType: comment.commentCreatorEntity,
    }));

    const creatorsMap = await this.batchGetCommentCreators(creatorIds);

    const relevantCommentsMap = new Map();

    for (const [postId, commentsForPost] of commentsByPost) {
      const selectedComment = this.selectMostRelevantFromBatch(commentsForPost);
      const creatorKey = `${selectedComment.commentCreatorEntity}:${selectedComment.commentCreatorId}`;
      const creator = creatorsMap.get(creatorKey);

      if (creator && selectedComment) {
        const topLikesCount = selectedComment.likesCount;
        const hasMultipleCommentsWithSameLikes =
          commentsForPost.length > 1 && topLikesCount === commentsForPost[1].likesCount;

        relevantCommentsMap.set(postId, {
          id: selectedComment.commentId,
          content: selectedComment.comment,
          creator,
          hasMoreThanOneAnswer: hasMultipleCommentsWithSameLikes,
          commentHasLikes: topLikesCount > 0,
          isPinned: false,
          createdAt: selectedComment.createdAt,
        });
      }
    }

    return relevantCommentsMap;
  }

  /**
   * Select most relevant comment from batch results
   */
  private selectMostRelevantFromBatch(commentsForPost: any[]) {
    if (commentsForPost.length === 0) return null;

    // Comments are already sorted by:
    // 1. Most likes first (DESC)
    // 2. Earliest to reach current like count (ASC on latestLikeTime)

    const topComment = commentsForPost[0];
    const topLikesCount = topComment.likesCount;

    // If no likes on the top comment, return the most recent comment
    if (topLikesCount === 0) {
      return commentsForPost.reduce((mostRecent, comment) =>
        new Date(comment.createdAt) > new Date(mostRecent.createdAt) ? comment : mostRecent,
      );
    }

    // If there are likes, the query already sorted correctly:
    // - Most likes first
    // - Among comments with same likes, earliest to reach that count first
    return topComment;
  }

  /**
   * Batch fetch comment creators (users and workspace creators)
   */
  private async batchGetCommentCreators(
    creatorIds: { id: string; entityType: string }[],
  ): Promise<Map<string, CreatorDetails>> {
    const creatorsMap = new Map<string, CreatorDetails>();

    // Separate user and workspace creator IDs
    const userIds = creatorIds
      .filter((creator) => creator.entityType === EntityType.USER)
      .map((creator) => creator.id);

    const workspaceIds = creatorIds
      .filter((creator) => creator.entityType === EntityType.WORKSPACE)
      .map((creator) => creator.id);

    // Batch fetch users
    if (userIds.length > 0) {
      const userResults = await this.db.query.users.findMany({
        columns: {
          id: true,
          profileImageUrlThumbnail: true,
          username: true,
          displayName: true,
        },
        where: inArray(users.id, userIds),
      });

      for (const userResult of userResults) {
        creatorsMap.set(`${EntityType.USER}:${userResult.id}`, userResult);
      }
    }

    // Batch fetch workspace creators
    if (workspaceIds.length > 0) {
      const workspaceResults = await this.db.query.workspaces.findMany({
        columns: {
          id: true,
        },
        where: inArray(workspaces.id, workspaceIds),
        with: {
          createdByUser: {
            columns: {
              username: true,
              id: true,
              profileImageUrlThumbnail: true,
              displayName: true,
            },
          },
        },
      });

      for (const workspaceResult of workspaceResults) {
        creatorsMap.set(
          `${EntityType.WORKSPACE}:${workspaceResult.id}`,
          workspaceResult.createdByUser,
        );
      }
    }

    return creatorsMap;
  }

  async mapQuotedCaptionsWithUserDetails(quotedCaptions: QuotedCaption[]) {
    if (!quotedCaptions || quotedCaptions.length === 0) {
      return [];
    }

    const userIds = quotedCaptions
      .filter((qc) => qc.entityType === EntityType.USER)
      .map((qc) => qc.entityId);

    const workspaceIds = quotedCaptions
      .filter((qc) => qc.entityType === EntityType.WORKSPACE)
      .map((qc) => qc.entityId);

    const usersData = userIds.length
      ? await this.db.query.users.findMany({
          columns: {
            id: true,
            displayName: true,
            username: true,
            profileImageUrlThumbnail: true,
          },
          where: inArray(users.id, userIds),
        })
      : [];

    const workspacesData = workspaceIds.length
      ? await this.db.query.workspaces.findMany({
          columns: {
            id: true,
          },
          where: inArray(workspaces.id, workspaceIds),
          with: {
            createdByUser: {
              columns: {
                id: true,
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        })
      : [];

    const usersMap = new Map(usersData.map((u) => [`${EntityType.USER}:${u.id}`, u]));
    const workspacesMap = new Map(
      workspacesData.map((w) => [`${EntityType.WORKSPACE}:${w.id}`, w.createdByUser]),
    );

    const combinedMap = new Map([...usersMap, ...workspacesMap]);

    return quotedCaptions.map((qc) => {
      const details = combinedMap.get(`${qc.entityType}:${qc.entityId}`);
      return {
        entityId: qc.entityId,
        entityType: qc.entityType,
        caption: qc.caption,
        displayName: details?.displayName || null,
        username: details?.username || null,
        profileImageUrlThumbnail: details?.profileImageUrlThumbnail || null,
      };
    });
  }

  async findAll({
    limit,
    offset,
    entityId,
    searchKeyword,
    postTypes,
    filterOutReposts = false, // Optional flag
  }: {
    limit: number;
    offset: number;
    entityId?: string;
    searchKeyword?: string;
    postTypes?: PostType[];
    filterOutReposts?: boolean;
  }) {
    const normalizedKeyword = searchKeyword?.toLowerCase().replace(/\s+/g, '');

    const keywordFilter = normalizedKeyword
      ? or(
          exists(
            this.db
              .select()
              .from(postTags)
              .where(
                and(
                  eq(postTags.postId, posts.id),
                  eq(postTags.status, PostTagsStatus.ACTIVE),
                  exists(
                    this.db
                      .select()
                      .from(dbTags)
                      .where(
                        and(
                          eq(dbTags.id, postTags.tagId),
                          ilike(dbTags.name, `%${normalizedKeyword}%`),
                        ),
                      ),
                  ),
                ),
              ),
          ),
          exists(
            this.db
              .select()
              .from(workspaces)
              .where(
                and(
                  eq(workspaces.id, posts.workspaceId),
                  exists(
                    this.db
                      .select()
                      .from(users)
                      .where(
                        and(
                          eq(users.id, workspaces.createdById),
                          sql`LOWER(REPLACE(${users.displayName}, ' ', '')) LIKE ${`%${normalizedKeyword}%`}`,
                        ),
                      ),
                  ),
                ),
              ),
          ),
          sql`${posts.contentTsv} @@ plainto_tsquery('english', ${normalizedKeyword})`,
        )
      : undefined;

    const postTypeFilter = postTypes?.length ? inArray(posts.postType, postTypes) : undefined;
    const repostFilter = filterOutReposts ? isNull(posts.repostOfPostId) : undefined;

    const repostPosts = alias(posts, 'repost_posts');

    const rawPosts = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        tags: true,
        content: true,
        workspaceId: true,
        audience: true,
        community: true,
        repostOfPostId: true,
        originalPostId: true,
        quotedCaptions: true,
      },
      where: and(
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        ne(posts.postStatus, PostStatus.DRAFT),
        keywordFilter,
        postTypeFilter,
        repostFilter,
      ),
      orderBy: (post, { desc: descending }) => [descending(post.postScheduleDate)],
      with: {
        articlePost: {
          columns: {
            title: true,
            coverImagePath: true,
          },
        },
        pollPost: {
          columns: {
            question: true,
            durationDays: true,
            expiresAt: true,
            allowCustomAnswer: true,
          },
        },
        linkPost: {
          columns: {
            link: true,
          },
        },
        publishedBy: {
          columns: {
            displayName: true,
            username: true,
            profileImageUrlThumbnail: true,
          },
        },
        workspace: {
          columns: {},
          with: {
            createdByUser: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
        },
        repostOf: {
          columns: {
            id: true,
            content: true,
            community: true,
            audience: true,
            postType: true,
            tags: true,
            repostOfPostId: true,
            createdAt: true,
            status: true,
          },
          with: {
            publishedBy: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
            postMedias: {
              columns: {
                mediaPath: true,
                mediaType: true,
                altText: true,
              },
              where: eq(postMedias.status, PostMediaStatus.ACTIVE),
              orderBy: asc(postMedias.order),
            },
          },
        },
        originalPost: {
          columns: {
            id: true,
            content: true,
            community: true,
            audience: true,
            postType: true,
            tags: true,
            repostOfPostId: true,
            createdAt: true,
            status: true,
          },
          with: {
            publishedBy: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
            postMedias: {
              columns: {
                mediaPath: true,
                mediaType: true,
                altText: true,
              },
              where: eq(postMedias.status, PostMediaStatus.ACTIVE),
              orderBy: asc(postMedias.order),
            },
          },
        },
      },
      extras: (fields) => ({
        postedAt: sql`${posts.postScheduleDate}`.as('posted_at'),
        ...isLikedByEntity('isLiked', fields.id, entityId),
        ...countRelation(
          'commentsCount',
          fields.id,
          postComments.postId,
          PostCommentsStatus.ACTIVE,
        ),
        ...countRelation('likesCount', fields.id, postLikes.postId, PostLikesStatus.ACTIVE),
        repostCount: sql<number>`(
          ${this.db
            .select({ count: count() })
            .from(repostPosts)
            .where(
              and(
                eq(repostPosts.repostOfPostId, fields.id),
                eq(repostPosts.status, PostActiveStatus.ACTIVE),
              ),
            )}::int
        )`.as('repostCount'),
        shareCount: sql<number>`0`.as('share_count'),
      }),
      limit,
      offset,
    });

    rawPosts.forEach((post) => {
      if (post.repostOf && post.repostOf.status !== PostActiveStatus.ACTIVE) {
        post.repostOf = null;
      }
      if (post.originalPost && post.originalPost.status !== PostActiveStatus.ACTIVE) {
        post.originalPost = null;
      }
    });

    const questionPostIds = rawPosts
      .filter((post) => post.postType === PostType.QUESTION)
      .map((post) => post.id);

    const pollPostIds = rawPosts
      .filter((post) => post.postType === PostType.POLL)
      .map((post) => post.id);

    const featuredComments =
      questionPostIds.length > 0 ? await this.batchGetFeaturedComments(questionPostIds) : new Map();

    const pollsData =
      pollPostIds.length > 0
        ? await this.pollPostsService.batchGetPollData(pollPostIds, entityId)
        : new Map();

    const postsWithComments = rawPosts.map(async (dbPost) => {
      type Post = typeof dbPost;

      const post: Omit<Post, 'workspace' | 'publishedBy'> &
        Partial<Pick<Post, 'workspace' | 'publishedBy'>> = {
        ...dbPost,
      };

      const { displayName: publisherName, ...publisherDetails } =
        dbPost.workspace?.createdByUser ?? dbPost.publishedBy;
      delete post.workspace;
      delete post.publishedBy;

      let flattenedPostTypeData: Record<string, any> = {};
      let featuredComment = null;
      let pollData = null;

      for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
        const typedFieldKey = fieldKey as keyof Post;
        if (post.postType === postTypeKey) {
          flattenedPostTypeData = post[typedFieldKey] as Record<string, any>;

          if (post.postType === PostType.QUESTION) {
            featuredComment = featuredComments.get(post.id) || null;
          }

          if (post.postType === PostType.POLL) {
            pollData = pollsData.get(post.id) || null;
          }

          delete post[typedFieldKey];
        } else {
          delete post[typedFieldKey];
        }
      }

      const enrichedQuotedCaptions = await this.mapQuotedCaptionsWithUserDetails(
        post.quotedCaptions || [],
      );

      return {
        ...post,
        ...flattenedPostTypeData,
        ...(post.postType === PostType.QUESTION ? { featuredComment } : {}),
        ...(post.postType === PostType.POLL ? pollData : {}),
        ...publisherDetails,
        publisherName,
        quotedCaptions: enrichedQuotedCaptions,
      };
    });

    return Promise.all(postsWithComments);
  }

  async findOne({ postId, entityId }: { postId: string; entityId: string }) {
    const repostPosts = alias(posts, 'repost_posts');

    const rawPost = await this.db.query.posts.findFirst({
      columns: {
        id: true,
        postType: true,
        tags: true,
        content: true,
        workspaceId: true,
        audience: true,
        community: true,
        repostOfPostId: true,
        originalPostId: true,
        quotedCaptions: true,
      },
      where: and(
        eq(posts.id, postId),
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        ne(posts.postStatus, PostStatus.DRAFT),
      ),
      with: {
        articlePost: {
          columns: {
            title: true,
            coverImagePath: true,
            body: true,
          },
        },
        pollPost: {
          columns: {
            question: true,
            durationDays: true,
            expiresAt: true,
            allowCustomAnswer: true,
          },
        },
        publishedBy: {
          columns: {
            displayName: true,
            username: true,
            profileImageUrlThumbnail: true,
          },
        },
        linkPost: {
          columns: {
            link: true,
          },
        },
        workspace: {
          columns: {},
          with: {
            createdByUser: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
        },
        repostOf: {
          columns: {
            id: true,
            content: true,
            community: true,
            audience: true,
            postType: true,
            tags: true,
            repostOfPostId: true,
            createdAt: true,
            status: true,
          },
          with: {
            publishedBy: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
            postMedias: {
              columns: {
                mediaPath: true,
                mediaType: true,
                altText: true,
              },
              where: eq(postMedias.status, PostMediaStatus.ACTIVE),
              orderBy: asc(postMedias.order),
            },
          },
        },
        originalPost: {
          columns: {
            id: true,
            content: true,
            community: true,
            audience: true,
            postType: true,
            tags: true,
            repostOfPostId: true,
            createdAt: true,
            status: true,
          },
          with: {
            publishedBy: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
            postMedias: {
              columns: {
                mediaPath: true,
                mediaType: true,
                altText: true,
              },
              where: eq(postMedias.status, PostMediaStatus.ACTIVE),
              orderBy: asc(postMedias.order),
            },
          },
        },
      },
      extras: (fields) => ({
        postedAt: sql`${posts.postScheduleDate}`.as('posted_at'),
        ...isLikedByEntity('isLiked', fields.id, entityId),
        ...countRelation(
          'commentsCount',
          fields.id,
          postComments.postId,
          PostCommentsStatus.ACTIVE,
        ),
        ...countRelation('likesCount', fields.id, postLikes.postId, PostLikesStatus.ACTIVE),
        repostCount: sql<number>`(
          ${this.db
            .select({ count: count() })
            .from(repostPosts)
            .where(
              and(
                eq(repostPosts.repostOfPostId, fields.id),
                eq(repostPosts.status, PostActiveStatus.ACTIVE),
              ),
            )}::int
        )`.as('repostCount'),
        shareCount: sql<number>`0`.as('share_count'),
      }),
    });

    if (rawPost) {
      if (rawPost.repostOf && rawPost.repostOf.status !== PostActiveStatus.ACTIVE) {
        rawPost.repostOf = null;
      }
      if (rawPost.originalPost && rawPost.originalPost.status !== PostActiveStatus.ACTIVE) {
        rawPost.originalPost = null;
      }
    }

    if (!rawPost) throw itemNotFound(EntityName.POST);

    // Flatten post-type-specific data
    let flattenedPostTypeData: Record<string, any> = {};
    let featuredComment = null;
    let pollData = null;

    for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
      const typedFieldKey = fieldKey as keyof typeof rawPost;
      if (rawPost.postType === postTypeKey) {
        flattenedPostTypeData = rawPost[typedFieldKey] as Record<string, any>;

        if (rawPost.postType === PostType.QUESTION) {
          const featuredComments = await this.batchGetFeaturedComments([rawPost.id]);
          featuredComment = featuredComments.get(rawPost.id) || null;
        }

        if (rawPost.postType === PostType.POLL) {
          const pollsData = await this.pollPostsService.batchGetPollData([rawPost.id], entityId);
          pollData = pollsData.get(rawPost.id) || null;
        }

        delete (rawPost as any)[typedFieldKey];
      } else {
        delete (rawPost as any)[typedFieldKey];
      }
    }

    // Extract publisher
    const { displayName: publisherName, ...publisherDetails } =
      rawPost.workspace?.createdByUser ?? rawPost.publishedBy;

    const enrichedQuotedCaptions = await this.mapQuotedCaptionsWithUserDetails(
      rawPost.quotedCaptions || [],
    );

    return {
      ...rawPost,
      ...flattenedPostTypeData,
      ...(rawPost.postType === PostType.QUESTION ? { featuredComment } : {}),
      ...(rawPost.postType === PostType.POLL ? pollData : {}),
      ...publisherDetails,
      publisherName,
      quotedCaptions: enrichedQuotedCaptions,
    };
  }

  async findOneScheduledOrDraftPost({
    postId,
    entityId,
    entityType,
  }: {
    postId: string;
    entityId: string;
    entityType: EntityType;
  }) {
    const whereCondition =
      entityType === EntityType.WORKSPACE
        ? and(
            eq(posts.id, postId),
            eq(posts.workspaceId, entityId),
            eq(posts.status, PostActiveStatus.ACTIVE),
            or(eq(posts.postStatus, PostStatus.SCHEDULED), eq(posts.postStatus, PostStatus.DRAFT)),
          )
        : and(
            eq(posts.id, postId),
            eq(posts.publishedById, entityId),
            eq(posts.status, PostActiveStatus.ACTIVE),
            isNull(posts.workspaceId),
            or(eq(posts.postStatus, PostStatus.SCHEDULED), eq(posts.postStatus, PostStatus.DRAFT)),
          );

    const rawPost = await this.db.query.posts.findFirst({
      columns: {
        id: true,
        content: true,
        tags: true,
        audience: true,
        community: true,
        postScheduleDate: true,

        postType: true,
        postStatus: true,
      },
      where: whereCondition,
      with: {
        articlePost: {
          columns: {
            title: true,
            body: true,
            coverImagePath: true,
          },
        },
        pollPost: {
          columns: {
            question: true,
            durationDays: true,
            allowCustomAnswer: true,
          },
          with: {
            options: {
              columns: {
                text: true,
                order: true,
              },
              where: eq(pollOptions.status, PollPostOptionsStatus.ACTIVE),
              orderBy: asc(pollOptions.order),
            },
          },
        },
        linkPost: {
          columns: {
            link: true,
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
        },
      },
    });

    const isExpiredScheduledPost =
      rawPost?.postStatus === PostStatus.SCHEDULED && rawPost?.postScheduleDate < new Date();

    if (!rawPost || isExpiredScheduledPost) throw itemNotFound(EntityName.POST);

    const post = { ...rawPost };

    let flattenedPostTypeData: Record<string, any> = {};

    for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
      const typedFieldKey = fieldKey as keyof typeof rawPost;

      if (post.postType === postTypeKey) {
        flattenedPostTypeData = post[typedFieldKey] as Record<string, any>;
      }

      delete post[typedFieldKey];
    }

    return { ...post, ...flattenedPostTypeData };
  }

  async findAllByTag({
    tagName,
    limit,
    offset,
    entityId,
    searchKeyword,
    postTypes,
  }: {
    tagName: string;
    limit: number;
    offset: number;
    entityId?: string;
    searchKeyword?: string;
    postTypes?: PostType[];
  }) {
    const normalizedKeyword = searchKeyword?.toLowerCase().replace(/\s+/g, '');

    const keywordFilter = normalizedKeyword
      ? or(
          // Match publisher's display name (without spaces)
          exists(
            this.db
              .select()
              .from(workspaces)
              .where(
                and(
                  eq(workspaces.id, posts.workspaceId),
                  exists(
                    this.db
                      .select()
                      .from(users)
                      .where(
                        and(
                          eq(users.id, workspaces.createdById),
                          sql`LOWER(REPLACE(${users.displayName}, ' ', '')) LIKE ${`%${normalizedKeyword}%`}`,
                        ),
                      ),
                  ),
                ),
              ),
          ),
          // Match post content
          sql`${posts.contentTsv} @@ plainto_tsquery('english', ${normalizedKeyword})`,
        )
      : undefined;

    const postTypeFilter = postTypes?.length ? inArray(posts.postType, postTypes) : undefined;

    const tagFilter = exists(
      this.db
        .select()
        .from(postTags)
        .where(
          and(
            eq(postTags.postId, posts.id),
            eq(postTags.status, PostTagsStatus.ACTIVE),
            exists(
              this.db
                .select()
                .from(dbTags)
                .where(
                  and(
                    eq(dbTags.id, postTags.tagId),
                    sql`LOWER(${dbTags.name}) = ${tagName.toLowerCase()}`,
                  ),
                ),
            ),
          ),
        ),
    );

    const repostPosts = alias(posts, 'repost_posts');

    const rawPosts = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        tags: true,
        content: true,
        workspaceId: true,
        community: true,
        audience: true,
        repostOfPostId: true,
        originalPostId: true,
        quotedCaptions: true,
      },
      where: and(
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        ne(posts.postStatus, PostStatus.DRAFT),
        tagFilter,
        keywordFilter,
        postTypeFilter,
      ),
      orderBy: desc(posts.postScheduleDate),
      with: {
        articlePost: {
          columns: {
            title: true,
            coverImagePath: true,
          },
        },
        pollPost: {
          columns: {
            question: true,
            durationDays: true,
            expiresAt: true,
            allowCustomAnswer: true,
          },
        },
        repostOf: {
          columns: {
            id: true,
            content: true,
            community: true,
            audience: true,
            postType: true,
            tags: true,
            repostOfPostId: true,
            createdAt: true,
            status: true,
          },
          with: {
            publishedBy: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
            postMedias: {
              columns: {
                mediaPath: true,
                mediaType: true,
                altText: true,
              },
              where: eq(postMedias.status, PostMediaStatus.ACTIVE),
              orderBy: asc(postMedias.order),
            },
          },
        },
        originalPost: {
          columns: {
            id: true,
            content: true,
            community: true,
            audience: true,
            postType: true,
            tags: true,
            repostOfPostId: true,
            createdAt: true,
            status: true,
          },
          with: {
            publishedBy: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
            postMedias: {
              columns: {
                mediaPath: true,
                mediaType: true,
                altText: true,
              },
              where: eq(postMedias.status, PostMediaStatus.ACTIVE),
              orderBy: asc(postMedias.order),
            },
          },
        },
        linkPost: {
          columns: {
            link: true,
          },
        },
        publishedBy: {
          columns: {
            displayName: true,
            username: true,
            profileImageUrlThumbnail: true,
          },
        },
        workspace: {
          columns: {},
          with: {
            createdByUser: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
        },
      },
      extras: (fields) => ({
        postedAt: sql`${posts.postScheduleDate}`.as('postedAt'),
        ...isLikedByEntity('isLiked', fields.id, entityId),
        ...countRelation(
          'commentsCount',
          fields.id,
          postComments.postId,
          PostCommentsStatus.ACTIVE,
        ),
        ...countRelation('likesCount', fields.id, postLikes.postId, PostLikesStatus.ACTIVE),
        shareCount: sql<number>`0`.as('share_count'),
        repostCount: sql<number>`(
          ${this.db
            .select({ count: count() })
            .from(repostPosts)
            .where(
              and(
                eq(repostPosts.repostOfPostId, fields.id),
                eq(repostPosts.status, PostActiveStatus.ACTIVE),
              ),
            )}::int
        )`.as('repostCount'),
      }),
      limit,
      offset,
    });

    rawPosts.forEach((post) => {
      if (post.repostOf && post.repostOf.status !== PostActiveStatus.ACTIVE) {
        post.repostOf = null;
      }
      if (post.originalPost && post.originalPost.status !== PostActiveStatus.ACTIVE) {
        post.originalPost = null;
      }
    });

    const questionPostIds = rawPosts
      .filter((post) => post.postType === PostType.QUESTION)
      .map((post) => post.id);

    const pollPostIds = rawPosts
      .filter((post) => post.postType === PostType.POLL)
      .map((post) => post.id);

    const featuredComments =
      questionPostIds.length > 0 ? await this.batchGetFeaturedComments(questionPostIds) : new Map();

    const pollsData =
      pollPostIds.length > 0
        ? await this.pollPostsService.batchGetPollData(pollPostIds, entityId)
        : new Map();

    const transformedPosts = await Promise.all(
      rawPosts.map(async (dbPost) => {
        type Post = typeof dbPost;

        const post: Omit<Post, 'workspace' | 'publishedBy'> &
          Partial<Pick<Post, 'workspace' | 'publishedBy'>> = { ...dbPost };

        const { displayName: publisherName, ...publisherDetails } =
          dbPost.workspace?.createdByUser ?? dbPost.publishedBy;
        delete post.workspace;
        delete post.publishedBy;

        let flattenedPostTypeData: Record<string, any> = {};
        let featuredComment = null;
        let pollData = null;

        for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
          const typedFieldKey = fieldKey as keyof Post;
          if (post.postType === postTypeKey) {
            flattenedPostTypeData = post[typedFieldKey] as Record<string, any>;

            if (post.postType === PostType.QUESTION) {
              featuredComment = featuredComments.get(post.id) || null;
            }
            if (post.postType === PostType.POLL) {
              pollData = pollsData.get(post.id) || null;
            }

            delete post[typedFieldKey];
          } else {
            delete post[typedFieldKey];
          }
        }

        const enrichedQuotedCaptions = await this.mapQuotedCaptionsWithUserDetails(
          post.quotedCaptions || [],
        );

        return {
          ...post,
          ...flattenedPostTypeData,
          ...(post.postType === PostType.QUESTION ? { featuredComment } : {}),
          ...(post.postType === PostType.POLL ? pollData : {}),
          ...publisherDetails,
          publisherName,
          quotedCaptions: enrichedQuotedCaptions,
        };
      }),
    );

    return {
      posts: transformedPosts,
      isFollowed: false,
    };
  }

  async createPost(data: NewPost, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;

    const [post] = await queryRunner.insert(posts).values(data).returning();
    return post;
  }

  async updatePost(
    postId: string,
    updateData: Partial<NewPost>,
    updatedById: string,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    // Verify post exists
    const existingPost = await this.getPostById(postId);

    if (!existingPost) {
      throw itemNotFound(EntityName.POST);
    }

    const [updatedPost] = await queryRunner
      .update(posts)
      .set({
        ...updateData,
        updatedById,
      })
      .where(and(eq(posts.id, postId), eq(posts.status, PostActiveStatus.ACTIVE)))
      .returning();

    if (!updatedPost) throw itemNotFound(EntityName.POST);

    return updatedPost;
  }

  // Get a post by ID
  async getPostById(postId: string) {
    const post = await this.db.query.posts.findFirst({
      where: eq(posts.id, postId),
      with: {
        publishedBy: true,
        updatedBy: true,
        postTags: {
          with: {
            tag: true,
          },
        },
      },
    });

    if (!post) {
      throw itemNotFound(EntityName.POST);
    }

    return post;
  }

  // CHECK THE PERMISSION TO CREATE POST FOR AUDIENCE WHICH IS PASSED IN PARAMETE
  async checkAudiencePermission(
    workpsacePrimarySpeciality: PrimarySpeciality,
    audience: PrimarySpeciality,
  ) {
    // If workspace has BOTH speciality, they can choose any audience
    if (workpsacePrimarySpeciality === PrimarySpeciality.BOTH) {
      return; // Allow any audience selection
    }

    // If workspace speciality doesn't match the audience (and isn't BOTH)
    if (workpsacePrimarySpeciality !== audience) {
      throw audiencePermissionError();
    }
  }

  // Get all draft posts for a workspace
  async getDraftPosts(workspaceId: string) {
    const postsData = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        postScheduleDate: true,
        content: true,
      },
      where: and(
        eq(posts.workspaceId, workspaceId),
        eq(posts.postStatus, PostStatus.DRAFT),
        eq(posts.status, PostActiveStatus.ACTIVE),
      ),
      with: {
        articlePost: {
          columns: {
            title: true,
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          limit: 1,
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
          extras: {
            totalMediasCount:
              sql<number>`(select count(*) from ${schema.postMedias} where ${schema.postMedias.postId} = ${schema.posts.id})`.as(
                'total_medias_count',
              ),
          },
        },
      },
      orderBy: asc(posts.postScheduleDate),
    });

    return postsData.map((postData) => {
      const post: any = { ...postData };

      // Only flatten articlePost if present
      if (post.postType === PostType.ARTICLE && post.articlePost) {
        Object.assign(post, post.articlePost);
        delete post.articlePost;
      }

      return post;
    });
  }

  // Get all scheduled posts for a workspace
  async getScheduledPosts(workspaceId: string) {
    const now = new Date();

    const postsData = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        postScheduleDate: true,
        content: true,
      },
      where: and(
        eq(posts.workspaceId, workspaceId),
        eq(posts.postStatus, PostStatus.SCHEDULED),
        eq(posts.status, PostActiveStatus.ACTIVE),
        gte(posts.postScheduleDate, now), // Only get posts scheduled for the future
      ),
      with: {
        articlePost: {
          columns: {
            title: true,
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          limit: 1,
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
          extras: {
            totalMediasCount:
              sql<number>`(select count(*) from ${schema.postMedias} where ${schema.postMedias.postId} = ${schema.posts.id})`.as(
                'total_medias_count',
              ),
          },
        },
      },
      orderBy: asc(posts.postScheduleDate),
    });

    return postsData.map((postData) => {
      const post: any = { ...postData };

      // Only flatten articlePost if present
      if (post.postType === PostType.ARTICLE && post.articlePost) {
        Object.assign(post, post.articlePost);
        delete post.articlePost;
      }

      return post;
    });
  }

  // Validation logic for both public and workspace posts
  private async validateAndPreparePost(workspaceId: string | undefined, dto: CommonPostCreateDto) {
    // For public posts (no workspaceId)
    if (!workspaceId) {
      // Set default values for public posts
      dto.community = CommunityType.PUBLIC;
      dto.audience = undefined;

      // Ensure public posts have the correct tag
      if (dto.tags.length !== 1 || dto.tags[0] !== DEFAULT_PUBLIC_POST_TAG) {
        throw publicPostMustHaveSingleTag();
      }

      return null; // No workspace details for public posts
    }

    // For workspace posts, validate that community and audience are provided
    if (!dto.community) {
      throw communityIsRequiredForWorkspacePost();
    }
    if (!dto.audience) {
      throw audienceIsRequiredForWorkspacePost();
    }

    // For workspace posts, validate workspace and audience
    const workspacesDetails = await this.workspacesService.findOneWorkspaceByWorkspaceId(
      workspaceId,
      true,
    );

    // Check if the workspace has permission to create post for the specified audience
    await this.checkAudiencePermission(workspacesDetails.primarySpeciality, dto.audience);

    return workspacesDetails;
  }

  private isContentEmpty(content: string | null | undefined): boolean {
    if (!content) return true;
    const textOnly = content.replace(/<[^>]*>/g, '').trim();
    return textOnly === '';
  }

  async getAdditionalTextRepostsDetails(dto: CreateTextPostDto) {
    if (dto.repostOfPostId) {
      // Prepare the quoted captions
      const repostedPost = await this.db.query.posts.findFirst({
        where: and(eq(posts.id, dto.repostOfPostId), eq(posts.status, PostActiveStatus.ACTIVE)),
      });

      if (!repostedPost) {
        throw itemNotFound(EntityName.POST);
      }
      // Ensure quotedCaptions is always an array
      const existingQuotes = Array.isArray(repostedPost.quotedCaptions)
        ? repostedPost.quotedCaptions
        : [];

      if (dto.includeQuote) {
        let additionalTextPostDetails;

        // eslint-disable-next-line unicorn/prefer-ternary
        if (repostedPost.originalPostId) {
          additionalTextPostDetails = !this.isContentEmpty(repostedPost.content)
            ? {
                repostOfPostId: repostedPost.id,
                originalPostId: repostedPost.originalPostId,
                quotedCaptions: [
                  {
                    entityId: repostedPost.workspaceId
                      ? repostedPost.workspaceId
                      : repostedPost.publishedById,
                    entityType: repostedPost.workspaceId ? EntityType.WORKSPACE : EntityType.USER,
                    caption: repostedPost.content || '',
                  },
                  ...existingQuotes,
                ],
              }
            : {
                repostOfPostId: repostedPost.repostOfPostId
                  ? repostedPost.repostOfPostId
                  : repostedPost.originalPostId,
                originalPostId: repostedPost.originalPostId,
                quotedCaptions: [...existingQuotes],
              };
        } else {
          additionalTextPostDetails = {
            repostOfPostId: repostedPost.id,
            originalPostId: repostedPost.id,
            quotedCaptions: [],
          };
        }

        return additionalTextPostDetails;
      } else {
        const repostOfPostId = repostedPost.repostOfPostId
          ? repostedPost.originalPostId
          : repostedPost.id;

        const originalPostId = repostedPost.originalPostId
          ? repostedPost.originalPostId
          : repostedPost.id;

        const additionalTextPostDetails = {
          repostOfPostId,
          originalPostId,
          quotedCaptions: [],
        };

        return additionalTextPostDetails;
      }
    } else {
      return {
        repostOfPostId: null,
        originalPostId: null,
        quotedCaptions: [],
      };
    }
  }

  // Common post creation logic for both public and workspace posts
  async createPostWithTransaction<T extends CommonPostCreateDto>(
    dto: T,
    publisherId: string,
    workspaceId: string | undefined,
    postType: PostType,
    specificPostHandler?: (postId: string, tx: PostgresJsDatabase<typeof schema>) => Promise<void>,
  ): Promise<string> {
    // Validate permissions and prepare post data
    await this.validateAndPreparePost(workspaceId, dto);
    // Start transaction
    return this.db.transaction(async (tx) => {
      // Create the tags
      const newTags = await this.tagsService.createMissingTags(dto.tags, tx);

      // Create the post
      const newPost = await this.createPost(
        {
          ...dto,
          community: dto.community as CommunityType,
          publishedById: publisherId,
          updatedById: publisherId,
          workspaceId,
          status: PostActiveStatus.ACTIVE,
          postType,
          tags: newTags.map((tag) => tag.name),
          // quotedCaptions: dto.quotedCaptions ? dto.quotedCaptions : [],
        },
        tx,
      );

      // Associate tags with the post
      await this.postTagsService.associateTagsWithPost(
        newPost.id,
        newTags.map((tag) => tag.id),
        tx,
      );

      // Handle post-type specific logic
      await specificPostHandler?.(newPost.id, tx);

      return newPost.id;
    });
  }

  async handlePostUpdate(
    postId: string,
    userId: string,
    workspaceId: string | undefined,
    updateData: UpdateCommonPostCreateDto,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const { postStatus, postScheduleDate, audience, community, tags, content } = updateData;

    // Get the post to check ownership and type
    const post = await this.getPostById(postId);

    // Validate ownership based on post type (public vs workspace)
    if (post.workspaceId) {
      // This is a workspace post
      if (!workspaceId || post.workspaceId !== workspaceId) {
        throw unauthorized();
      }
    } else {
      // This is a public post - only the publisher can update it
      if (post.publishedById !== userId) {
        throw unauthorized();
      }

      // For public posts, validate tag restrictions
      if (tags && (tags.length !== 1 || tags[0] !== DEFAULT_PUBLIC_POST_TAG)) {
        throw publicPostMustHaveSingleTag();
      }

      // For public posts, ensure community and audience are set correctly
      if (community && community !== CommunityType.PUBLIC) {
        throw publicPostMustBePublicCommunity();
      }

      if (audience !== undefined && audience !== null) {
        throw publicPostCannotHaveAudience();
      }
    }

    // Check if post is already live (published or scheduled time has passed)
    const now = new Date();
    const existingScheduledDate = new Date(post.postScheduleDate);

    const isPostLive =
      post.postStatus === PostStatus.PUBLISHED ||
      (post.postStatus === PostStatus.SCHEDULED && existingScheduledDate < now);

    // If trying to update status or schedule date, check if post is already live
    if ((postStatus || postScheduleDate) && isPostLive) {
      throw invalidPostStatusTransitionException();
    }

    let newPostScheduledDate: Date | undefined;
    if (postStatus || postScheduleDate) {
      // updation
      if ([PostStatus.DRAFT, PostStatus.PUBLISHED].includes(postStatus as PostStatus)) {
        newPostScheduledDate = new Date();
      } else if (postStatus === PostStatus.SCHEDULED) {
        // since there is a validation inside the dto, the schedule post will be there
        newPostScheduledDate = postScheduleDate;
      } else if (!postStatus && postScheduleDate) {
        // check in case if they didnt passed the post status, but only scheudled date. then update it if it's only scheduled post.
        if (post.postStatus === PostStatus.SCHEDULED) {
          const scheduleDate = new Date(postScheduleDate);
          const currentTime = new Date();
          currentTime.setMinutes(now.getMinutes() + 10); // Ensure it's at least 10 minutes later
          if (scheduleDate < currentTime) throw postScheduleDateMinimum10MinutesRequired();
          newPostScheduledDate = postScheduleDate;
        } else {
          // incase it's draft post
          newPostScheduledDate = postScheduleDate;
        }
      }
    }

    // Check audience permission if changing audience (only for workspace posts)
    if (audience && workspaceId) {
      const workspacesDetails = await this.workspacesService.findOneWorkspaceByWorkspaceId(
        workspaceId,
        true,
      );

      await this.checkAudiencePermission(
        workspacesDetails?.primarySpeciality as PrimarySpeciality,
        audience,
      );
    }

    // Handle tags if provided
    const newTags = tags ? await this.tagsService.createMissingTags(tags, tx) : [];

    // Update post tags only if tags are provided
    if (tags) {
      await this.postTagsService.updatePostTags(
        postId,
        newTags.map((tag) => tag.id),
        tx,
      );
    }

    // Update the base post
    return this.updatePost(
      postId,
      {
        content,
        postStatus,
        postScheduleDate: newPostScheduledDate,
        audience,
        community,
        tags: tags ? newTags.map((tag) => tag.name) : undefined,
      },
      userId,
      tx,
    );
  }

  /**
   * Soft deletes a post by setting its status to INACTIVE
   * @param postId - The ID of the post to soft delete
   * @param workspaceId - The ID of the workspace that owns the post (null for public posts)
   * @param userId - The ID of the user (for public posts)
   * @returns The updated post ID
   * @throws {itemNotFound} If the post is not found or doesn't match criteria
   * @throws {unauthorized} If user doesn't have permission to delete
   */
  async softDeletePost(postId: string, workspaceId: string | null, userId?: string) {
    // Build the where condition based on post type
    if (!workspaceId && !userId) {
      throw unauthorized();
    }

    const whereCondition = workspaceId
      ? and(
          eq(posts.id, postId),
          eq(posts.workspaceId, workspaceId),
          eq(posts.status, PostActiveStatus.ACTIVE),
        )
      : and(
          eq(posts.id, postId),

          eq(posts.publishedById, userId!), // Non-null assertion is safe here
          eq(posts.status, PostActiveStatus.ACTIVE),
          isNull(posts.workspaceId),
        );

    // Update the post status to INACTIVE
    const [updatedPost] = await this.db
      .update(posts)
      .set({
        status: PostActiveStatus.INACTIVE,
      })
      .where(whereCondition)
      .returning({
        id: posts.id,
      });

    if (!updatedPost) {
      throw itemNotFound(EntityName.POST);
    }

    return { id: updatedPost.id };
  }
}
