import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { alias } from 'drizzle-orm/pg-core';
import { and, desc, eq, inArray, isNull, or, sql } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  postComments,
  posts,
  questionPosts,
  users,
  postCommentLikes,
  workspaces,
} from '@/db/schema';

import { CreateCommentDto } from './dto/create-post-comment';
import { UpdateCommentDto } from './dto/update-post-comment';

import { PostCommentsStatus } from '@/constants/post-comments';
import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';
import { PostActiveStatus } from '@/constants/posts';
import { UserStatus } from '@/constants/users';
import { PostCommentLikesStatus } from '@/constants/post-comment-likes';
// import { WorkspacesStatus } from '@/constants/workspaces';

import { itemNotFound } from '@/exceptions/common';
import {
  mentionedUserNotFound,
  onlyTopLevelCommentCanBeUsedAsParent,
  parentCommentNotFound,
} from '@/exceptions/posts';

@Injectable()
export class PostCommentsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createPostComment(
    postId: string,
    entityId: string,
    entityType: EntityType,
    createCommentDto: CreateCommentDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.db;

    const { parentCommentId, comment } = createCommentDto;

    // validate the parent comment id is valid and is top level comment
    if (parentCommentId) {
      const parentComment = await db.query.postComments.findFirst({
        where: and(
          eq(postComments.id, parentCommentId),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
        ),
      });

      if (!parentComment) throw parentCommentNotFound();

      if (parentComment.parentCommentId !== null) throw onlyTopLevelCommentCanBeUsedAsParent();
    }

    const mentionRegex = /@\[([^\]]+)\]\(([a-zA-Z0-9_.-]+)\)/g;

    const mentionedUsers = this.extractUserIds(mentionRegex, comment, 2);
    const uniqueUsernames = new Set(mentionedUsers);

    const validUsers = uniqueUsernames.size
      ? await db.query.users.findMany({
          where: and(
            inArray(users.username, [...uniqueUsernames]),
            eq(users.status, UserStatus.ACTIVE),
          ),
          columns: { id: true, username: true },
        })
      : [];

    // Check if any mentioned username is invalid
    if (uniqueUsernames.size > 0 && validUsers.length !== uniqueUsernames.size) {
      throw mentionedUserNotFound();
    }

    // Map: username -> userId
    const usernameToIdMap = new Map(validUsers.map((u) => [u.username, u.id]));

    const processed = uniqueUsernames.size
      ? comment.replace(mentionRegex, (match, _name, username) => {
          const id = usernameToIdMap.get(username);
          return id ? `⟨${id}⟩` : match;
          // NOTE:
          // We are using ⟨ ⟩ (Unicode mathematical angle brackets U+27E8 and U+27E9)
          // instead of any other symbols for the internal mention placeholder.
          //
          // Why?
          // - These characters are NOT available on a standard keyboard, making them extremely unlikely
          //   to be typed by users manually in a comment.
          // - That ensures we can reliably distinguish between mentions inserted by the frontend mention UI
          //   (which we process and replace with ⟨user-id⟩),
          //   and any random user-typed text that may resemble a placeholder.
          // - This prevents misuse, spoofing, or accidental false mentions.
          //
          // Later, while rendering, only ⟨user-id⟩ placeholders will be parsed and converted
          // back into proper mentions (e.g. @[Name](user-id)).
          //
          // This pattern guarantees clear intent, security, and cleaner mention parsing logic.
        })
      : comment;

    // Validate all the uuid with the users table. and store parsed value. check if the comment contain <userId> if check that as well

    const [newComment] = await db
      .insert(postComments)
      .values({
        postId,
        entityId,
        entityType,
        comment: processed,
        parentCommentId,
        status: PostCommentsStatus.ACTIVE,
      })
      .returning({ id: postComments.id });

    return newComment;
  }

  private extractUserIds(regex: RegExp, input: string, groupIndex: number) {
    return Array.from(input.matchAll(regex)).map((m) => m[groupIndex]);
  }

  async getComments({
    postId,
    limit,
    offset,
    parentCommentId,
    entityId,
    entityType,
  }: {
    postId: string;
    limit: number;
    offset: number;
    parentCommentId?: string | null;
    entityId: string;
    entityType: EntityType;
  }) {
    const replies = alias(postComments, 'replies');

    // Main comment creator
    const userAlias = alias(users, 'u');
    const workspaceAlias = alias(workspaces, 'w');
    const workspaceCreatorAlias = alias(users, 'wu');

    // Like entity
    const likeUser = alias(users, 'lu');
    const likeWorkspace = alias(workspaces, 'lw');
    const likeWorkspaceCreator = alias(users, 'lwu');

    const replyUser = alias(users, 'ru');
    const replyWorkspace = alias(workspaces, 'rw');
    const replyWorkspaceCreator = alias(users, 'rwu');

    if (parentCommentId) {
      const parent = await this.db
        .select({
          id: postComments.id,
        })
        .from(postComments)
        .leftJoin(
          users,
          and(
            eq(users.id, postComments.entityId),
            eq(postComments.entityType, EntityType.USER),
            eq(users.status, UserStatus.ACTIVE),
          ),
        )
        .leftJoin(
          workspaces,
          and(
            eq(workspaces.id, postComments.entityId),
            eq(postComments.entityType, EntityType.WORKSPACE),
            // eq(workspaces.status, WorkspacesStatus.ACTIVE),
          ),
        )
        .leftJoin(
          workspaceCreatorAlias,
          and(
            eq(workspaceCreatorAlias.id, workspaces.createdById),
            eq(workspaceCreatorAlias.status, UserStatus.ACTIVE),
          ),
        )
        .where(
          and(
            eq(postComments.id, parentCommentId),
            eq(postComments.status, PostCommentsStatus.ACTIVE),
            or(
              and(
                eq(postComments.entityType, EntityType.USER),
                eq(users.status, UserStatus.ACTIVE),
              ),
              and(
                eq(postComments.entityType, EntityType.WORKSPACE),
                // eq(workspaces.status, WorkspacesStatus.ACTIVE),
                eq(workspaceCreatorAlias.status, UserStatus.ACTIVE),
              ),
            ),
          ),
        )
        .limit(1);

      if (parent.length === 0) throw parentCommentNotFound();
    }

    const comments = await this.db
      .select({
        id: postComments.id,
        comment: postComments.comment,
        createdAt: postComments.createdAt,

        likesCount: sql<number>`
          CAST(COUNT(
            DISTINCT CASE
              WHEN ${postCommentLikes.status} = ${PostCommentLikesStatus.ACTIVE}
                AND ${postCommentLikes.entityType} = ${EntityType.USER}
                AND ${likeUser.status} = ${UserStatus.ACTIVE}
              THEN ${postCommentLikes.entityId}
              WHEN ${postCommentLikes.status} = ${PostCommentLikesStatus.ACTIVE}
                AND ${postCommentLikes.entityType} = ${EntityType.WORKSPACE}
                AND ${likeWorkspaceCreator.status} = ${UserStatus.ACTIVE}
              THEN ${postCommentLikes.entityId}
              ELSE NULL
            END
          ) AS INT)
        `.as('likesCount'),

        /* 
             AND ${likeWorkspace.status} = ${WorkspacesStatus.ACTIVE}
         add above line with

        WHEN ${postCommentLikes.status} = ${PostCommentLikesStatus.ACTIVE}
                AND ${postCommentLikes.entityType} = ${EntityType.WORKSPACE}
                AND ${likeWorkspaceCreator.status} = ${UserStatus.ACTIVE}
        */

        repliesCount: sql<number>`
          CAST(COUNT(
            DISTINCT CASE
              WHEN ${replies.status} = ${PostCommentsStatus.ACTIVE}
                AND ${replies.entityType} = ${EntityType.USER}
                AND ${replyUser.status} = ${UserStatus.ACTIVE}
              THEN ${replies.id}
              WHEN ${replies.status} = ${PostCommentsStatus.ACTIVE}
                AND ${replies.entityType} = ${EntityType.WORKSPACE}
                AND ${replyWorkspaceCreator.status} = ${UserStatus.ACTIVE}
              THEN ${replies.id}
              ELSE NULL
            END
          ) AS INT)
        `.as('repliesCount'),

        /* 
              AND ${replyWorkspace.status} = ${WorkspacesStatus.ACTIVE}
         add above line with

         WHEN ${replies.status} = ${PostCommentsStatus.ACTIVE}
              AND ${replies.entityType} = ${EntityType.WORKSPACE}
              AND ${replyWorkspaceCreator.status} = ${UserStatus.ACTIVE}
        
        */

        isLiked: sql<boolean>`
          CASE WHEN EXISTS (
            SELECT 1
            FROM ${postCommentLikes}
            WHERE ${postCommentLikes.postCommentId} = ${postComments.id}
              AND ${postCommentLikes.entityId} = ${entityId}
              AND ${postCommentLikes.entityType} = ${entityType}
              AND ${postCommentLikes.status} = ${PostCommentLikesStatus.ACTIVE}
          )
          THEN TRUE ELSE FALSE END
        `.as('isLiked'),

        // Creator info
        userDisplayName: userAlias.displayName,
        userUsername: userAlias.username,
        userProfileImageUrlThumbnail: userAlias.profileImageUrlThumbnail,

        workspaceDisplayName: workspaceCreatorAlias.displayName,
        workspaceUsername: workspaceCreatorAlias.username,
        workspaceProfileImageUrlThumbnail: workspaceCreatorAlias.profileImageUrlThumbnail,

        entityType: postComments.entityType,
      })
      .from(postComments)

      // Replies join
      .leftJoin(
        replies,
        and(
          eq(replies.parentCommentId, postComments.id),
          // eq(replies.status, PostCommentsStatus.ACTIVE),
        ),
      )

      .leftJoin(
        replyUser,
        and(eq(replyUser.id, replies.entityId), eq(replies.entityType, EntityType.USER)),
      )
      .leftJoin(
        replyWorkspace,
        and(eq(replyWorkspace.id, replies.entityId), eq(replies.entityType, EntityType.WORKSPACE)),
      )
      .leftJoin(
        replyWorkspaceCreator,
        and(eq(replyWorkspaceCreator.id, replyWorkspace.createdById)),
      )

      // Likes join
      .leftJoin(
        postCommentLikes,
        and(
          eq(postCommentLikes.postCommentId, postComments.id),
          eq(postCommentLikes.status, PostCommentLikesStatus.ACTIVE),
        ),
      )

      // Liking user
      .leftJoin(
        likeUser,
        and(
          eq(likeUser.id, postCommentLikes.entityId),
          eq(postCommentLikes.entityType, EntityType.USER),
          eq(likeUser.status, UserStatus.ACTIVE),
        ),
      )

      // Liking workspace
      .leftJoin(
        likeWorkspace,
        and(
          eq(likeWorkspace.id, postCommentLikes.entityId),
          eq(postCommentLikes.entityType, EntityType.WORKSPACE),
          // eq(likeWorkspace.status, WorkspacesStatus.ACTIVE),
        ),
      )

      // Liking workspace's creator
      .leftJoin(
        likeWorkspaceCreator,
        and(
          eq(likeWorkspaceCreator.id, likeWorkspace.createdById),
          eq(likeWorkspaceCreator.status, UserStatus.ACTIVE),
        ),
      )

      // Comment creator (user)
      .leftJoin(
        userAlias,
        and(
          eq(userAlias.id, postComments.entityId),
          eq(postComments.entityType, EntityType.USER),
          eq(userAlias.status, UserStatus.ACTIVE),
        ),
      )

      // Comment creator (workspace)
      .leftJoin(
        workspaceAlias,
        and(
          eq(workspaceAlias.id, postComments.entityId),
          eq(postComments.entityType, EntityType.WORKSPACE),
          // eq(workspaceAlias.status, WorkspacesStatus.ACTIVE),
        ),
      )

      // Workspace's creator
      .leftJoin(
        workspaceCreatorAlias,
        and(
          eq(workspaceCreatorAlias.id, workspaceAlias.createdById),
          eq(workspaceCreatorAlias.status, UserStatus.ACTIVE),
        ),
      )

      // WHERE clause — no filtering for likes here!
      .where(
        and(
          eq(postComments.postId, postId),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
          parentCommentId
            ? eq(postComments.parentCommentId, parentCommentId)
            : isNull(postComments.parentCommentId),

          // Only include comment if creator is valid
          or(
            and(
              eq(postComments.entityType, EntityType.USER),
              eq(userAlias.status, UserStatus.ACTIVE),
            ),
            and(
              eq(postComments.entityType, EntityType.WORKSPACE),
              // eq(workspaceAlias.status, WorkspacesStatus.ACTIVE),
              eq(workspaceCreatorAlias.status, UserStatus.ACTIVE),
            ),
          ),
        ),
      )

      .groupBy(postComments.id, userAlias.id, workspaceCreatorAlias.id)

      .orderBy(desc(postComments.createdAt))
      .limit(limit)
      .offset(offset);

    const mentionPlaceholderRegex = /⟨([a-zA-Z0-9-]+)⟩/g;
    const mentionedUserIds = new Set<string>();

    for (const comment of comments) {
      const userIds = this.extractUserIds(mentionPlaceholderRegex, comment.comment, 1);

      userIds.forEach((m) => mentionedUserIds.add(m));
    }

    const mentionedUsers = mentionedUserIds.size
      ? await this.db.query.users.findMany({
          where: and(inArray(users.id, [...mentionedUserIds]), eq(users.status, UserStatus.ACTIVE)),
          columns: {
            id: true,
            displayName: true,
            username: true,
          },
        })
      : [];

    const userMap = new Map(mentionedUsers.map((user) => [user.id, user]));

    // STEP 3: Replace mentions in comment text
    return comments.map((comment) => {
      const isUser = comment.entityType === EntityType.USER;

      // Replace ⟨user-id⟩ with @[Display Name](user-id)
      const formattedComment = comment.comment.replace(
        mentionPlaceholderRegex,
        (_, userId: string) => {
          const user = userMap.get(userId);
          return user ? `@[${user.displayName}](${user.username})` : `@[unknown user](${userId})`;
        },
      );

      return {
        id: comment.id,
        comment: formattedComment,
        createdAt: comment.createdAt,
        likesCount: comment.likesCount,
        repliesCount: comment.repliesCount,
        isLiked: comment.isLiked,
        creator: {
          displayName: isUser ? comment.userDisplayName : comment.workspaceDisplayName,
          username: isUser ? comment.userUsername : comment.workspaceUsername,
          profileImageUrlThumbnail: isUser
            ? comment.userProfileImageUrlThumbnail
            : comment.workspaceProfileImageUrlThumbnail,
        },
      };
    });
  }

  async updatePostComment(commentId: string, entityId: string, comment: UpdateCommentDto) {
    const [res] = await this.db
      .update(postComments)
      .set({
        comment: comment.comment,
      })
      .where(
        and(
          eq(postComments.id, commentId),
          eq(postComments.entityId, entityId),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.POST_COMMENT);

    return res;
  }

  async softDeletePostComment(
    id: string,
    entityId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.db;

    const [res] = await db
      .update(postComments)
      .set({
        status: PostCommentsStatus.INACTIVE,
      })
      .where(
        and(
          eq(postComments.id, id),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
          eq(postComments.entityId, entityId),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.POST_COMMENT);

    return res;
  }

  async getCommentWithPostDetails(commentId: string) {
    const [commentDetails] = await this.db
      .select({
        postId: postComments.postId,
        postType: posts.postType,
        workspaceId: posts.workspaceId,
        pinnedCommentId: questionPosts.pinnedCommentId,
      })
      .from(postComments)
      .innerJoin(posts, eq(postComments.postId, posts.id))
      .innerJoin(questionPosts, eq(posts.id, questionPosts.postId))
      .where(
        and(
          eq(postComments.id, commentId),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
          eq(posts.status, PostActiveStatus.ACTIVE),
        ),
      )
      .limit(1);

    return commentDetails;
  }
}
