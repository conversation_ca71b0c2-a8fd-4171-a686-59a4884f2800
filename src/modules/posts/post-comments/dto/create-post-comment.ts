import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsString, IsNotEmpty, MaxLength, MinLength, IsUUID, IsOptional } from 'class-validator';

import { MAX_LENGTH, MIN_LENGTH } from '@/constants/posts';

export class CreateCommentDto {
  @ApiProperty({
    name: 'comment',
    type: 'string',
    required: true,
    example: 'very informative',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  comment: string;

  @ApiPropertyOptional({
    name: 'parentCommentId',
    type: 'string',
    required: false,
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The ID of the parent comment if this is a reply',
  })
  @IsUUID()
  @IsOptional()
  parentCommentId?: string;
}
