import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNumber, IsOptional, IsUUID, <PERSON>, Min } from 'class-validator';

export class CommentFetchDto {
  @ApiPropertyOptional({
    description: 'The ID of the connected user you want to remove from your connection list',
    required: false,
    type: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  parentCommentId: string;

  @ApiPropertyOptional({
    description: 'Maximum number of results to return, default is 20',
    example: 20,
    required: false,
    default: 20,
  })
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(1)
  @Max(50)
  limit: number = 20;

  @ApiPropertyOptional({
    description: 'Number of results to skip (offset), used for pagination/infinite scroll',
    example: 0,
    required: false,
    default: 0,
  })
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(0)
  offset: number = 0;
}
