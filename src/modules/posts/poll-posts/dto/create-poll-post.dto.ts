import {
  ArrayMaxSize,
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsString,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { CommonPostCreateDto } from '@/modules/posts/dto/create-post.dto';

import { MAX_LENGTH, MIN_LENGTH, PollPostDurationDays } from '@/constants/posts';

export class CreatePollPostDto extends CommonPostCreateDto {
  @ApiProperty({
    name: 'content',
    type: 'string',
    required: true,
    example: 'Curious to see how clinical practice is evolving—thanks for participating!',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  @IsString()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  content: string;

  @ApiProperty({
    name: 'question',
    type: 'string',
    required: true,
    example:
      'With the growing body of evidence supporting the use of SGLT2 inhibitors in HFpEF patients, are you now routinely prescribing them as part of your initial management plan?',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  @IsString()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  question: string;

  @ApiProperty({
    name: 'durationDays',
    enum: PollPostDurationDays,
    required: true,
    example: PollPostDurationDays.ONE,
    description: 'Poll duration in days',
  })
  @IsNotEmpty()
  @IsEnum(PollPostDurationDays)
  durationDays: PollPostDurationDays;

  @ApiProperty({
    name: 'allowCustomAnswer',
    type: 'boolean',
    required: true,
    example: true,
    description: 'Whether users can add custom answers',
  })
  @IsNotEmpty()
  @IsBoolean()
  allowCustomAnswer: boolean;

  @ApiProperty({
    name: 'options',
    type: [String],
    required: true,
    example: ['Red', 'Blue', 'Green'],
    description: '2-10 unique options (case-insensitive)',
  })
  @IsArray()
  @ArrayUnique((o: string) => o.trim().toLowerCase())
  @IsString({ each: true })
  @ArrayMinSize(2)
  @ArrayMaxSize(10)
  @IsNotEmpty({ each: true })
  options: string[];
}
