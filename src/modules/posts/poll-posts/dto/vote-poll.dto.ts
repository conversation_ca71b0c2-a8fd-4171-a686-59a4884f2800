import {
  IsUUI<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ValidateIf,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

// Custom validator to ensure exactly one of two fields is provided
function IsExactlyOneOf(property: string, validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isExactlyOneOf',
      target: object.constructor,
      propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];

          // Exactly one should be defined (not undefined and not empty)
          const currentFieldHasValue = value !== undefined && value !== null && value !== '';
          const relatedFieldHasValue =
            relatedValue !== undefined && relatedValue !== null && relatedValue !== '';

          return currentFieldHasValue !== relatedFieldHasValue; // XOR logic
        },
        defaultMessage(args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          return `Either ${args.property} or ${relatedPropertyName} must be provided, but not both`;
        },
      },
    });
  };
}

export class VotePollDto {
  @ValidateIf((o) => o.customText === undefined) // Only validate if customText is not provided
  @IsUUID()
  optionId?: string;

  @ValidateIf((o) => o.optionId === undefined) // Only validate if optionId is not provided
  @IsString()
  @MinLength(1)
  @MaxLength(200)
  @IsExactlyOneOf('optionId')
  customText?: string;
}
