import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, asc, count, desc, eq, exists, ilike, inArray, sql } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { pollPosts, pollOptions, NewPollOption, posts, pollVotes, PollPost } from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import {
  alreadyHasCustomOptionException,
  cannotAddCustomOptionAfterVoteException,
  cannotUpdateExpiredPollException,
  cannotUpdateVotedPollException,
  pollExpiredException,
} from '@/exceptions/posts';

import { EntityName } from '@/constants/entities';
import {
  PollPostDurationDays,
  PollPostOptionsStatus,
  PollVoteStatus,
  PostActiveStatus,
  PostType,
} from '@/constants/posts';
import { EntityType } from '@/constants/user-types';

import { VotePollDto } from './dto/vote-poll.dto';

@Injectable()
export class PollPostsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createPollPost(
    {
      postId,
      question,
      durationDays,
      allowCustomAnswer,
      options,
      postScheduleDate,
    }: {
      postId: string;
      question: string;
      durationDays: PollPostDurationDays;
      allowCustomAnswer: boolean;
      options: string[];
      postScheduleDate: Date;
    },
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    // Create poll_posts row
    const [pollPost] = await queryRunner
      .insert(pollPosts)
      .values({
        postId,
        question,
        durationDays,
        expiresAt: new Date(postScheduleDate.getTime() + durationDays * 24 * 60 * 60 * 1000),
        allowCustomAnswer,
      })
      .returning();

    const pollOptionsToInsert: NewPollOption[] = options.map((optionText, idx) => ({
      pollId: pollPost.postId,
      text: optionText.trim(),
      isCustom: false,
      order: idx + 1,
      status: PollPostOptionsStatus.ACTIVE,
    }));

    await queryRunner.insert(pollOptions).values(pollOptionsToInsert);

    return pollPost;
  }

  async updatePollPost(
    postId: string,
    updatePollPostDto: {
      question?: string;
      durationDays?: PollPostDurationDays;
      allowCustomAnswer?: boolean;
      options?: string[];
      postScheduleDate?: Date;
    },
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    // Check if poll exists
    const existingPollData = await queryRunner.query.pollPosts.findFirst({
      where: eq(pollPosts.postId, postId),
      columns: {
        expiresAt: true,
        durationDays: true,
      },
    });

    if (!existingPollData) {
      throw itemNotFound(EntityName.POST);
    }

    // Check if poll has expired
    if (new Date(existingPollData.expiresAt).getTime() < Date.now()) {
      throw cannotUpdateExpiredPollException();
    }

    const voteExists = await this.db
      .select({
        exists: exists(
          this.db
            .select()
            .from(pollVotes)
            .where(and(eq(pollVotes.pollId, postId), eq(pollVotes.status, PollVoteStatus.ACTIVE))),
        ),
      })
      .from(pollVotes)
      .where(eq(pollVotes.pollId, postId));

    if (voteExists[0]?.exists) {
      throw cannotUpdateVotedPollException();
    }

    const { options, postScheduleDate, durationDays, ...postData } = updatePollPostDto;

    // Check if we need to update poll post data
    const hasPostDataUpdates = Object.keys(postData).length > 0;
    const needsDateTimeUpdate = durationDays !== undefined || postScheduleDate !== undefined;

    if (needsDateTimeUpdate || hasPostDataUpdates) {
      let expiresAt: Date | undefined;
      let finalDurationDays: PollPostDurationDays | undefined;

      if (postScheduleDate && durationDays !== undefined) {
        // Case 1: Both present → calculate from scratch
        expiresAt = new Date(postScheduleDate.getTime() + durationDays * 24 * 60 * 60 * 1000);
        finalDurationDays = durationDays;
      } else if (postScheduleDate) {
        // Case 2: Only postScheduleDate present → use existing durationDays
        const duration = existingPollData.durationDays;
        expiresAt = new Date(postScheduleDate.getTime() + duration * 24 * 60 * 60 * 1000);
      } else if (durationDays !== undefined) {
        // Case 3: Only durationDays changed → shift existing expiresAt
        const previousDuration = existingPollData.durationDays;
        const durationDiff = durationDays - previousDuration;
        expiresAt = new Date(
          new Date(existingPollData.expiresAt).getTime() + durationDiff * 24 * 60 * 60 * 1000,
        );
        finalDurationDays = durationDays;
      }

      const updateData: Partial<PollPost> = { ...postData };
      if (expiresAt) updateData.expiresAt = expiresAt;
      if (finalDurationDays !== undefined) updateData.durationDays = finalDurationDays;

      await queryRunner
        .update(schema.pollPosts)
        .set(updateData)
        .where(eq(schema.pollPosts.postId, postId))
        .returning();
    }

    // Update options if provided
    if (options && options.length > 0) {
      // Delete only non-custom options.
      // We keep inactive custom answers (if any) for tracking purposes.
      // If a custom option were active, it would mean someone has already voted — but we’ve already prevented updates in such cases.
      await queryRunner
        .delete(pollOptions)
        .where(and(eq(pollOptions.pollId, postId), eq(pollOptions.isCustom, false)));

      // Insert new options
      const pollOptionsToInsert: NewPollOption[] = options.map((optionText, idx) => ({
        pollId: postId,
        text: optionText.trim(),
        isCustom: false,
        order: idx + 1,
        status: PollPostOptionsStatus.ACTIVE,
      }));

      await queryRunner.insert(pollOptions).values(pollOptionsToInsert);
    }
  }

  async getPollPostById(postId: string) {
    const pollPost = await this.db.query.posts.findFirst({
      where: and(
        eq(posts.status, PostActiveStatus.ACTIVE),
        eq(posts.id, postId),
        eq(posts.postType, PostType.POLL),
      ),
      with: {
        pollPost: true,
      },
    });

    if (!pollPost) throw itemNotFound(EntityName.POST);

    return pollPost;
  }

  async ensurePollNotExpired(pollId: string) {
    const poll = await this.db.query.pollPosts.findFirst({
      where: eq(pollPosts.postId, pollId),
    });

    if (!poll) throw itemNotFound(EntityName.POST);

    if (new Date() > poll.expiresAt) {
      throw pollExpiredException();
    }
  }

  async votePoll(pollId: string, voterId: string, voterType: EntityType, votePollDto: VotePollDto) {
    return this.db.transaction(async (tx) => {
      await this.ensurePollNotExpired(pollId);

      const existingVote = await tx.query.pollVotes.findFirst({
        where: and(
          eq(pollVotes.pollId, pollId),
          eq(pollVotes.entityId, voterId),
          eq(pollVotes.status, PollVoteStatus.ACTIVE),
        ),
      });

      if (existingVote) {
        const hasCustomOptionByUser = await tx.query.pollOptions.findFirst({
          where: and(
            eq(pollOptions.pollId, pollId),
            eq(pollOptions.isCustom, true),
            eq(pollOptions.createdByEntityId, voterId),
            eq(pollOptions.status, PollPostOptionsStatus.ACTIVE),
          ),
        });

        if (hasCustomOptionByUser) throw alreadyHasCustomOptionException();
      }

      const { optionId, customText } = votePollDto;

      // Case: Voting for an existing option
      if (optionId) {
        if (existingVote?.optionId === optionId) {
          throw itemAlreadyExists(EntityName.POLL_VOTE);
        }

        const selectedOption = await tx.query.pollOptions.findFirst({
          where: and(
            eq(pollOptions.id, optionId),
            eq(pollOptions.pollId, pollId),
            eq(pollOptions.status, PollPostOptionsStatus.ACTIVE),
          ),
        });

        if (!selectedOption) throw itemNotFound(EntityName.POLL_OPTION);

        if (existingVote) {
          await tx
            .update(pollVotes)
            .set({
              optionId,
            })
            .where(eq(pollVotes.id, existingVote.id));
        } else {
          const inactivePreviousVote = await tx.query.pollVotes.findFirst({
            where: and(
              eq(pollVotes.pollId, pollId),
              eq(pollVotes.entityId, voterId),
              eq(pollVotes.optionId, optionId),
              eq(pollVotes.status, PollVoteStatus.INACTIVE),
            ),
            orderBy: desc(pollVotes.createdAt),
          });

          await (inactivePreviousVote
            ? tx
                .update(pollVotes)
                .set({
                  status: PollVoteStatus.ACTIVE,
                })
                .where(eq(pollVotes.id, inactivePreviousVote.id))
            : tx.insert(pollVotes).values({
                pollId,
                optionId,
                entityId: voterId,
                entityType: voterType,
                status: PollVoteStatus.ACTIVE,
              }));
        }
      }

      // Case: Creating a new custom option
      else if (customText) {
        if (existingVote) throw cannotAddCustomOptionAfterVoteException();

        const similarOption = await tx.query.pollOptions.findFirst({
          where: and(
            eq(pollOptions.pollId, pollId),
            eq(pollOptions.status, PollPostOptionsStatus.ACTIVE),
            ilike(pollOptions.text, customText),
          ),
        });

        if (similarOption) throw itemAlreadyExists(EntityName.POLL_OPTION);

        const [optionCount] = await tx
          .select({ count: count(pollOptions.id) })
          .from(pollOptions)
          .where(eq(pollOptions.pollId, pollId));

        const [newOption] = await tx
          .insert(pollOptions)
          .values({
            pollId,
            text: customText,
            isCustom: true,
            createdByEntityId: voterId,
            createdByEntityType: voterType,
            order: optionCount.count + 1,
            status: PollPostOptionsStatus.ACTIVE,
          })
          .returning();

        await tx.insert(pollVotes).values({
          pollId,
          optionId: newOption.id,
          entityId: voterId,
          entityType: voterType,
          status: PollVoteStatus.ACTIVE,
        });
      }

      const updatedPollMap = await this.batchGetPollData([pollId], voterId, tx);
      const updatedPollResult = updatedPollMap.get(pollId);

      return {
        success: true,
        pollData: updatedPollResult,
      };
    });
  }

  async retractVote(pollId: string, voterId: string) {
    await this.ensurePollNotExpired(pollId);

    const activeVote = await this.db.query.pollVotes.findFirst({
      where: and(
        eq(pollVotes.pollId, pollId),
        eq(pollVotes.entityId, voterId),
        eq(pollVotes.status, PollVoteStatus.ACTIVE),
      ),
    });

    if (!activeVote) throw itemNotFound(EntityName.POLL_VOTE);

    const isCustomOptionCreatedByUser = await this.db.query.pollOptions.findFirst({
      where: and(
        eq(pollOptions.id, activeVote.optionId),
        eq(pollOptions.pollId, pollId),
        eq(pollOptions.isCustom, true),
        eq(pollOptions.createdByEntityId, voterId),
        eq(pollOptions.status, PollPostOptionsStatus.ACTIVE),
      ),
    });

    // eslint-disable-next-line unicorn/prefer-ternary
    if (isCustomOptionCreatedByUser) {
      await this.db.transaction(async (tx) => {
        await tx
          .update(pollOptions)
          .set({
            status: PollPostOptionsStatus.INACTIVE,
          })
          .where(
            and(
              eq(pollOptions.id, activeVote.optionId),
              eq(pollOptions.status, PollPostOptionsStatus.ACTIVE),
            ),
          );

        await tx
          .update(pollVotes)
          .set({
            status: PollVoteStatus.INACTIVE,
          })
          .where(
            and(
              eq(pollVotes.pollId, pollId),
              eq(pollVotes.optionId, activeVote.optionId),
              eq(pollVotes.status, PollVoteStatus.ACTIVE),
            ),
          );
      });
    } else {
      await this.db
        .update(pollVotes)
        .set({
          status: PollVoteStatus.INACTIVE,
        })
        .where(
          and(
            eq(pollVotes.id, activeVote.id),
            eq(pollVotes.pollId, pollId),
            eq(pollVotes.entityId, voterId),
            eq(pollVotes.status, PollVoteStatus.ACTIVE),
          ),
        );
    }
  }

  /**
   * Batch fetch poll data for multiple poll posts
   */
  async batchGetPollData(
    postIds: string[],
    entityId?: string,
    txn?: PostgresJsDatabase<typeof schema>,
  ): Promise<Map<string, any>> {
    const db = txn ?? this.db;

    const pollsMap = new Map();

    if (postIds.length === 0) return pollsMap;

    // Get poll basic data
    const pollsData = await db
      .select({
        postId: pollPosts.postId,
        question: pollPosts.question,
        durationDays: pollPosts.durationDays,
        expiresAt: pollPosts.expiresAt,
        allowCustomAnswer: pollPosts.allowCustomAnswer,
      })
      .from(pollPosts)
      .where(inArray(pollPosts.postId, postIds));

    // Get all poll options with vote counts
    const pollOptionsWithVotes = await db
      .select({
        pollId: pollOptions.pollId,
        optionId: pollOptions.id,
        text: pollOptions.text,
        isCustom: pollOptions.isCustom,
        createdByEntityId: pollOptions.createdByEntityId,
        createdByEntityType: pollOptions.createdByEntityType,
        order: pollOptions.order,
        votesCount: sql<number>`coalesce(count(${pollVotes.id}), 0)::int`,
      })
      .from(pollOptions)
      .leftJoin(
        pollVotes,
        and(eq(pollVotes.optionId, pollOptions.id), eq(pollVotes.status, PollVoteStatus.ACTIVE)),
      )
      .where(
        and(
          inArray(pollOptions.pollId, postIds),
          eq(pollOptions.status, PollPostOptionsStatus.ACTIVE),
        ),
      )
      .groupBy(
        pollOptions.id,
        pollOptions.pollId,
        pollOptions.text,
        pollOptions.isCustom,
        pollOptions.createdByEntityId,
        pollOptions.createdByEntityType,
        pollOptions.order,
      )
      .orderBy(asc(pollOptions.order));

    // Get user votes if entityId is provided
    const userVotesMap = new Map();
    if (entityId) {
      const userVotes = await db
        .select({
          pollId: pollVotes.pollId,
          optionId: pollVotes.optionId,
        })
        .from(pollVotes)
        .where(
          and(
            inArray(pollVotes.pollId, postIds),
            eq(pollVotes.entityId, entityId),
            eq(pollVotes.status, PollVoteStatus.ACTIVE),
          ),
        );

      userVotes.forEach((vote) => {
        userVotesMap.set(vote.pollId, vote.optionId);
      });
    }

    // Group options by poll
    const optionsByPoll = new Map<string, any[]>();
    for (const option of pollOptionsWithVotes) {
      if (!optionsByPoll.has(option.pollId)) {
        optionsByPoll.set(option.pollId, []);
      }
      optionsByPoll.get(option.pollId)!.push(option);
    }

    // Calculate total votes for each poll to get percentages
    const totalVotesByPoll = new Map<string, number>();
    for (const [pollId, options] of optionsByPoll) {
      const totalVotes = options.reduce((sum, option) => sum + option.votesCount, 0);
      totalVotesByPoll.set(pollId, totalVotes);
    }

    // Build final poll data
    for (const pollData of pollsData) {
      const now = new Date();
      const isExpired = new Date(pollData.expiresAt) < now;
      const userVotedOptionId = userVotesMap.get(pollData.postId);
      const hasUserVoted = !!userVotedOptionId;
      const totalVotes = totalVotesByPoll.get(pollData.postId) || 0;
      const options = optionsByPoll.get(pollData.postId) || [];

      // Process options
      const processedOptions = options.map((option) => {
        const percentage = totalVotes > 0 ? Math.round((option.votesCount / totalVotes) * 100) : 0;

        return {
          id: option.optionId,
          text: option.text,
          isCustom: option.isCustom,
          isCreatedByCurrentUser: entityId ? option.createdByEntityId === entityId : false,
          order: option.order,
          // Only show vote results if user has voted or poll is expired
          ...(hasUserVoted || isExpired
            ? {
                votesCount: option.votesCount,
                percentage,
              }
            : {}),
          // Mark the option user voted for
          ...(userVotedOptionId === option.optionId ? { isUserVote: true } : {}),
        };
      });

      pollsMap.set(pollData.postId, {
        question: pollData.question,
        durationDays: pollData.durationDays,
        expiresAt: pollData.expiresAt,
        allowCustomAnswer: pollData.allowCustomAnswer,
        isExpired,
        hasUserVoted,
        totalVotes,
        options: processedOptions,
      });
    }

    return pollsMap;
  }
}
