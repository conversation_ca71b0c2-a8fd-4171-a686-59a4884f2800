import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { linkPosts, NewLinkPost } from '@/db/schema';

import { itemNotFound } from '@/exceptions/common';
import { EntityName } from '@/constants/entities';

@Injectable()
export class LinkPostsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createLinkPost(postData: NewLinkPost, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;

    const [linkPost] = await queryRunner.insert(linkPosts).values(postData).returning();

    return linkPost;
  }

  async findOne(postId: string) {
    const [linkPost] = await this.db.select().from(linkPosts).where(eq(linkPosts.postId, postId));

    if (!linkPost) {
      throw itemNotFound(EntityName.LINK_POST);
    }

    return linkPost;
  }

  async updateLinkPost(
    postId: string,
    postData: Partial<NewLinkPost>,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    const [updatedLinkPost] = await queryRunner
      .update(linkPosts)
      .set(postData)
      .where(eq(linkPosts.postId, postId))
      .returning();

    if (!updatedLinkPost) {
      throw itemNotFound(EntityName.LINK_POST);
    }

    return updatedLinkPost;
  }

  async remove(postId: string) {
    const result = await this.db.delete(linkPosts).where(eq(linkPosts.postId, postId));

    // For Drizzle ORM with postgres-js, check the length of the result array
    if (result.length === 0) {
      throw itemNotFound(EntityName.LINK_POST);
    }

    return { message: 'Link post deleted successfully' };
  }
}
