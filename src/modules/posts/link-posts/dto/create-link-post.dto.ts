import { IsNotEmpty, IsString, IsUrl, IsOptional } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { CommonPostCreateDto } from '@/modules/posts/dto/create-post.dto';

export class CreateLinkPostDto extends CommonPostCreateDto {
  @ApiProperty({
    name: 'link',
    type: 'string',
    required: true,
    example: 'https://example.com/article',
    description: 'The URL to be shared in the link post',
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  @IsString()
  @IsUrl()
  link: string;

  @ApiProperty({
    name: 'content',
    type: 'string',
    required: false,
    example: 'Check out this interesting article!',
    description: 'The caption/content for the link post',
  })
  @IsOptional()
  @IsString()
  content?: string;
}
