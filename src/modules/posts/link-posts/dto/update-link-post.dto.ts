import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { IsDate, ValidateIf, IsOptional, IsUrl, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

import { PostStatus } from '@/constants/posts';

import { CreateLinkPostDto } from './create-link-post.dto';
import { IsFutureDateIfScheduled } from 'src/modules/posts/dto/create-post.dto';

export class UpdateLinkPostDto extends PartialType(
  OmitType(CreateLinkPostDto, ['postScheduleDate']),
) {
  @ApiProperty({
    name: 'postScheduleDate',
    type: 'string',
    required: false,
    example: '2024-12-31T23:59:59Z',
    description:
      'Required when postStatus is SCHEDULED. If publishing immediately, current date will be used.',
  })
  @ValidateIf((obj) => obj.postStatus === PostStatus.SCHEDULED)
  @IsDate()
  @IsFutureDateIfScheduled()
  @Transform(({ value }) => {
    if (value) {
      return new Date(value);
    }
  })
  postScheduleDate?: Date;

  @ApiProperty({
    name: 'link',
    type: 'string',
    required: false,
    example: 'https://example.com/updated-article',
    description: 'The updated URL for the link post',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Please provide a valid URL' })
  link?: string;

  @ApiProperty({
    name: 'content',
    type: 'string',
    required: false,
    example: 'Updated caption for the link',
    description: 'The updated caption/content for the link post',
  })
  @IsOptional()
  @IsString()
  content?: string;
}
