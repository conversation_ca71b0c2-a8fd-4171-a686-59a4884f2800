import {
  IsEnum,
  IsNotEmpty,
  IsString,
  IsArray,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
  ArrayUnique,
  IsDate,
  ValidateIf,
  IsIn,
  IsOptional,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { PrimarySpeciality } from '@/constants/workspaces';
import { CommunityType, PostStatus } from '@/constants/posts';

@ValidatorConstraint({ async: false })
class IsFutureDateIfScheduledConstraint implements ValidatorConstraintInterface {
  validate(postScheduleDate: string, args: ValidationArguments) {
    const { object } = args;
    if ((object as any).postStatus === PostStatus.SCHEDULED && postScheduleDate) {
      const scheduleDate = new Date(postScheduleDate);
      const now = new Date();
      now.setMinutes(now.getMinutes() + 10); // Ensure it's at least 10 minutes later
      return scheduleDate > now;
    }
    return true;
  }

  defaultMessage() {
    return `postScheduleDate must be at least 10 minutes in the future if postStatus is set to scheduled`;
  }
}

export function IsFutureDateIfScheduled(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsFutureDateIfScheduledConstraint,
    });
  };
}

export class CommonPostCreateDto {
  @ApiProperty({
    name: 'community',
    type: 'string',
    required: false,
    example: CommunityType.PROFESSIONAL,
    description: 'Optional. Will be set to PUBLIC if not provided and no workspaceId is present',
  })
  @IsEnum(CommunityType)
  community?: CommunityType;

  @ApiProperty({
    name: 'audience',
    type: 'string',
    required: false,
    example: PrimarySpeciality.BOTH,
    description: 'Optional. Required if workspaceId is present',
  })
  @IsOptional()
  @IsEnum(PrimarySpeciality)
  audience?: PrimarySpeciality;

  @ApiProperty({
    name: 'tags',
    type: [String],
    required: true,
    example: ['Innovation', 'News'],
  })
  @IsArray()
  @ArrayUnique()
  //@Type(() => String)
  @IsString({ each: true })
  tags: string[];

  @ApiProperty({
    name: 'postStatus',
    enum: PostStatus,
    required: true,
    example: PostStatus.PUBLISHED,
  })
  @IsNotEmpty()
  @IsEnum(PostStatus)
  @IsIn([PostStatus.DRAFT, PostStatus.PUBLISHED, PostStatus.SCHEDULED], {
    message: 'Invalid post status. Must be one of DRAFT, PUBLISHED, SCHEDULED.',
  })
  postStatus: PostStatus;

  @ApiProperty({
    name: 'postScheduleDate',
    type: 'string',
    required: false, // marked as not required here but validated conditionally
    example: '2024-12-31T23:59:59Z',
    description: 'If want to publish immediately, send current date',
  })
  @IsDate()
  @ValidateIf((obj) => obj.postStatus === PostStatus.SCHEDULED)
  @IsFutureDateIfScheduled()
  @Transform(({ obj, value }) => {
    const transformedValue = obj.postStatus === PostStatus.PUBLISHED ? new Date() : new Date(value);
    return transformedValue;
  })
  postScheduleDate: Date = new Date();
}
