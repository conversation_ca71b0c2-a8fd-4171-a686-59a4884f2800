import { HighlightType } from '@/constants/workspaces';
import {
  AllowedMediaType,
  CustomSectionLayout,
  CustomSectionType,
  DonationMethod,
  FundraiserLayout,
  MediaShape,
} from '@/constants/profile';

export type IHighlightItem =
  | { type: HighlightType.AWARD; title: string; year: number; institution: string }
  | { type: HighlightType.AFFILIATION; institution: string; year: number }
  | { type: HighlightType.ACCOMPLISHMENT; description: string };

export type IQualificationDetailsItem = {
  academicDegrees: {
    degree: string;
    specialisation?: string;
    yearOfAward: number;
    institution: string;
  }[];
  professionalDegrees: {
    degree: string;
    specialisation?: string;
    yearOfAward: number;
    institution: string;
  }[];
  registrationAndLicenses: {
    registeredWith: string;
    yearOfRegistration: number;
    details: string;
  }[];
};

export interface IRole {
  role: string;
  fromYear: number;
  toYear?: number | null;
}

export interface IWorkExperience {
  workplace: string;
  roles: IRole[];
}

export interface IAreaOfExpertise {
  title: string;
  description?: string;
}

export interface IClinicalInterest {
  title: string;
  description?: string;
}

export interface IService {
  title: string;
  description?: string;
}

export interface IFAQ {
  question: string;
  answer: string;
}

export type IGetInTouchDetails = {
  phones: {
    title?: string;
    phoneNumber: string;
  }[];
  emails: {
    title?: string;
    email: string;
  }[];
  address: {
    addressLine1: string;
    addressLine2: string;
    city: string;
    postcode: string;
    country: string;
  };
};

export type IShowcase = {
  mediaUrl: string;
  mediaType: string;
  title: string;
  description: string;
};

export type IFundraiserStyleOptions = {
  blurBg?: boolean;
  tintBgEnabled?: boolean;
  tintBgColor?: string;
  gradientColor?: string;
  textColor: string;
};

export type IFundraiserSection = {
  layout: FundraiserLayout;
  backgroundImageUrl: string;
  description: string;
  ctaText: string;
  styleOptions: IFundraiserStyleOptions;
  donationMethod: DonationMethod;
  donationLink?: string;
};

export type ICustomMediaStyleOptions = {
  mediaShape: MediaShape;
  tintBgEnabled: boolean;
  tintBgColor: string;
};

export type ICustomSection = {
  sectionType: CustomSectionType;
  layout?: CustomSectionLayout;
  mediaUrl?: string;
  mediaType?: AllowedMediaType;
  title: string;
  description: string;
  formatMedia?: ICustomMediaStyleOptions;
};
