// Coordinates for any point
export interface Coordinates {
  latitude: number;
  longitude: number;
}

// Properties of each feature
export interface FeatureProperties {
  feature_type: 'place' | 'region' | 'country';
  name: string;
  coordinates: Coordinates;
  mapbox_id: string;
}

// A single feature
export interface Feature {
  type: 'Feature';
  id: string;
  geometry: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
  properties: FeatureProperties;
}

// A collection of features
export interface FeatureCollection {
  type: 'FeatureCollection';
  features: Feature[];
}

// Final structured result
export interface LocationResult {
  place?: { name: string; coordinates: Coordinates; mapbox_id: string };
  region?: { name: string; coordinates: Coordinates; mapbox_id: string };
  country?: { name: string; coordinates: Coordinates; mapbox_id: string };
}

export interface MapboxContext {
  id: string;
  text: string;
}
export interface MapboxFeature {
  id: string;
  type: string;
  text: string;
  place_name: string;
  center: [number, number]; // [longitude, latitude]
  context?: MapboxContext[];
  properties?: {
    mapbox_id?: string;
    feature_type?: string;
    name?: string;
    coordinates?: { latitude: number; longitude: number };
  };
}
export interface MapboxGeocodingResponse {
  type: string;
  features: MapboxFeature[];
}
