import { Permissions } from '@/constants/permissions';
import { PublicRole, Role } from '@/constants/roles';
import { PrimarySpeciality, WorkspacesStatus } from '@/constants/workspaces';

export interface UserRolePermission {
  id: string;
  name: string;
  key: string;
}

export interface UserOrganizationRole {
  id: string;
  name: string;
  key: string;
  permissions: UserRolePermission[];
}

export interface UserOrganization {
  id: string;
  name: string;
  role: UserOrganizationRole;
}

export interface GetUserDetails {
  id: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  organisations: Record<string, UserOrganization>;
  globalRole?: {
    id: string;
    name: string;
    key: PublicRole;
    permissions: UserRolePermission[];
  };
}

export type GlobalPermission = {
  id: string;
  name: string;
  roleName: string;
  roleKey: Role;
  permissions: Permissions[];
};

export type WorkspacePermission = {
  id: string;
  workspaceId: string;
  roleName: string;
  status: WorkspacesStatus;
  roleKey: Role;
  permissions: Permissions[];
  primarySpeciality: PrimarySpeciality;
};
