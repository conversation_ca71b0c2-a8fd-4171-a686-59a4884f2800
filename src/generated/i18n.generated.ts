/* DO NOT EDIT, file generated by nestjs-i18n */
  
/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "auth": {
        "email_registered_not_verified": string;
        "user_cannot_be_created_without_any_role": string;
        "workspace_type_role_required_a_workspace_id": string;
        "global_type_role_should_not_need_a_workspace_id": string;
        "recent_sign_in_required": string;
        "cookie_not_found": string;
        "invalid_cookie": string;
        "session_expired": string;
        "invalid_credentials": string;
        "account_locked": string;
        "token_invalid": string;
        "token_expired": string;
        "user_no_workspace_access": string;
        "invalid_otp": string;
        "otp_expired": string;
    };
    "exception": {
        "not_found": string;
        "already_exists": string;
        "validation_error": string;
        "route_not_found": string;
        "unauthorized": string;
        "forbidden": string;
        "item_size_too_large": string;
        "limit_reached": string;
        "session_expired": string;
        "self_action_not_allowed": string;
    };
    "networking": {
        "connection_request_already_exists": string;
        "connection_request_already_received": string;
        "connection_request_not_found": string;
    };
    "opportunities": {
        "already_applied": string;
        "opportunity_expired": string;
    };
    "posts": {
        "post_schedule": {
            "minimum_10_minutes_required": string;
        };
        "country_id": {
            "post_in_unsubscribed_country": string;
        };
        "post_status": {
            "invalid_transition": string;
        };
        "reported_post": {
            "post_creator_cannot_report_a_post": string;
            "reported_post_is_not_modifiable": string;
        };
        "speciality_audience": {
            "invalid_speciality_audience": string;
        };
        "question_post": {
            "comment_not_a_question_post": string;
            "comment_already_pinned": string;
            "pinned_comment_not_found": string;
        };
        "comments": {
            "only_top_level_comment_can_be_used_as_parent": string;
            "mentioned_user_not_found": string;
            "parent_comment_not_found": string;
        };
        "poll_posts": {
            "poll_expired": string;
            "already_has_custom_option": string;
            "cannot_add_custom_option_after_vote": string;
            "cannot_update_voted_poll": string;
            "cannot_update_expired_poll": string;
        };
        "workspace_post": {
            "community_required": string;
            "audience_required": string;
        };
        "public_post": {
            "single_public_tag_required": string;
            "must_be_public_community": string;
            "cannot_have_audience": string;
        };
    };
    "profile": {
        "endYearMustBeGreaterThanStartYear": string;
        "reviews_disabled_for_viewing": string;
        "reviews_disabled_for_creation": string;
    };
    "roles": {
        "id_or_name_required": string;
    };
    "subscription": {
        "the_account_type_and_subscription_plan_user_chosen_is_not_matching": string;
        "professional_category_is_required_if_account_type_is_professional": string;
        "organization_application_fields_required": string;
        "subscription_does_not_allow_videos_or_highlights": string;
        "invalid_signature": string;
        "missing_metadata": string;
        "stripe_price_id_not_found": string;
    };
    "subtypes": {
        "error_messages": {
            "parent_subtype_not_found": string;
            "parent_subtype_belongs_to_different_account_type": string;
            "subtype_id_or_name_required": string;
        };
    };
    "workspaces": {
        "workspace_not_verified": string;
        "basic_and_primary_subscription_tier_user_cant_choose_both_for_mainly_works_with": string;
        "restricted_section_fields_based_on_account_or_subscription": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
