import { MEDIA_CATEGORY } from '@/constants/posts';

export const MIN_PROFILE_TEXT_LENGTH = 2;
export const MAX_PROFILE_TEXT_LENGTH = 3000;

export const MAX_QUALIFICATION_ITEMS = 10;

export const MAX_WORK_EXPERIENCE_ITEMS = 20;
export const MAX_WORK_EXPERIENCE_ROLES_ITEMS = 15;

export const MAX_CLINICAL_INTEREST_ITEMS = 10;

export const MAX_AREA_OF_EXPERTISE_ITEMS = 10;

export const MAX_FAQ_ITEMS = 10;

export const MAX_SERVICES_ITEMS = 10;

// get in touch section
export const MAX_CONTACT_TEXT_LENGTH = 100;
export const MIN_CONTACT_TEXT_LENGTH = 2;
export const MAX_CONTACT_ITEMS = 5; // Maximum 5 phones or emails
export const MAX_PHONE_LENGTH = 20;
export const MIN_PHONE_LENGTH = 8;

export const MAX_SHOWCASE_ITEMS = 10;
export const MAX_SHOWCASE_TEXT_LENGTH = 3000;
export const MIN_SHOWCASE_TEXT_LENGTH = 2;

export enum ProfileSection {
  QUALIFICATION = 'qualifications',
  WORK_EXPERIENCE = 'workExperiences',
  CLINICAL_INTEREST = 'clinicalInterests',
  AREA_OF_EXPERTISE = 'areaOfExpertises',
  SERVICE = 'services',
  FAQ = 'faqs',
  TEAM = 'team',
  SHOWCASE = 'showcases',
  GET_IN_TOUCH = 'getInTouch',
  FUNDRAISER = 'fundraiser',
  CUSTOM = 'custom',
}

export enum FundraiserLayout {
  BG_CENTER = 'bgCenter', // image as bg, text + CTA centered
  SIDE_IMAGE = 'sideImage', // image left, text + CTA right
  BG_LEFT_ALIGNED = 'bgLeftAligned', // image as bg, text + CTA left + gradient
}

export enum CustomSectionLayout {
  LEFT_ALIGNED_IMAGE = 'leftAlignedImage', // image left, title + description right
  RIGHT_ALIGNED_IMAGE = 'rightAlignedImage', // image right, title + description left
  TOP_ALIGNED_IMAGE = 'topAlignedImage', // image top, title + description down
}

export enum DonationMethod {
  MINICARDIAC_SERVICE = 'minicardiacService',
  DONATION_LINK = 'donationLink',
}

export enum CustomSectionType {
  MEDIA = 'media',
  TEXT = 'text',
}

export enum MediaShape {
  SQUARE = 'square',
  CIRCLE = 'circle',
}

export const ALLOWED_MEDIA = [MEDIA_CATEGORY.IMAGE, MEDIA_CATEGORY.VIDEO] as const;
export type AllowedMediaType = (typeof ALLOWED_MEDIA)[number]; // 'image' | 'video'

// export const SECTION_DESCRIPTIONS: Record<ProfileSection, string> = {
//   [ProfileSection.QUALIFICATION]: '',
//   [ProfileSection.WORK_EXPERIENCE]: '',
//   [ProfileSection.CLINICAL_INTEREST]: 'List and describe your clinical interests',
//   [ProfileSection.AREA_OF_EXPERTISE]: 'List and describe the details of what you do',
//   [ProfileSection.SERVICES]: 'List and describe the details of what you do',
//   [ProfileSection.FAQ]: 'Do you get the same questions a lot? Document your answers here',
//   [ProfileSection.TEAM]: 'Feature team members and link to their profiles',
//   [ProfileSection.SHOWCASE]: 'Create a carousel of your star products and services',
//   [ProfileSection.GET_IN_TOUCH]: 'Add a footer containing your contact details',
//   [ProfileSection.FUNDRAISE]: 'Raising money for a cause? We have your back.',
//   [ProfileSection.CUSTOM]:
//     'Did you have something else in mind? Customise this section however you like',
// };

export const MAX_REVIEW_MESSAGE = 3000;

export enum ReviewStatus {
  ACTIVE = 1,
  INACTIVE = 0,
}

export enum ReviewSortOrder {
  RECENT = 'recent',
  LOWEST = 'lowest',
  HIGHEST = 'highest',
}
