import type { Professionals, Specialists, Organisations, Students } from '@/db/schema';

export enum ProfessionalCategories {
  CARDIAC_SURGEON = 'CARDIAC_SURGEON',
  CARDIOLOGIST = 'CARDIOLOGIST',
  STUDENT = 'STUDENT',
  ALLIED_CARDIAC = 'ALLIED_CARDIAC',
}

export enum PrimarySpeciality {
  CARDIAC_SURGEON = ProfessionalCategories.CARDIAC_SURGEON,
  CARDIOLOGIST = ProfessionalCategories.CARDIOLOGIST,
  BOTH = 'BOTH',
}

export enum Speciality {
  CARDIAC_SURGEON = ProfessionalCategories.CARDIAC_SURGEON,
  CARDIOLOGIST = ProfessionalCategories.CARDIOLOGIST,
}

// First, let's define the relation names more strongly
export const WORKSPACE_RELATIONS = {
  ORGANISATION: 'organisations',
  SPECIALISTS: 'specialists',
  PROFESSIONAL: 'professionals',
  STUDENTS: 'students',
} as const;

export type AccountTypeInfo = Professionals | Specialists | Organisations | Students;

export enum WorkspaceUsersStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  ARCHIVED = -1,
}

export enum WorkspacesStatus {
  ACTIVE = 2,
  INACTIVE = 1,
  PENDING = 0,
  REJECTED = -1,
  ARCHIVED = -2,
}

// Role levels for workspace roles
export enum WorkspaceRoleLevel {
  ADMIN = 'ADMIN',
  USER = 'USER',
}

export enum StudentStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  ARCHIVED = -1,
}

// Highlight Type Enum
export enum HighlightType {
  AWARD = 'award',
  AFFILIATION = 'affiliation',
  ACCOMPLISHMENT = 'accomplishment',
}
