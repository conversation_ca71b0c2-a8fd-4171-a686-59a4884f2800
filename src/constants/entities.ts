export enum EntityName {
  USER = 'user',
  ROLE = 'role',
  PERMISSION = 'permission',
  WORKSPACE = 'workspace',
  WORKSPACE_USER = 'workspace user',
  ORGANISATION = 'organisation',
  MODULE = 'module',
  USER_ROLES = 'user role',
  ROLE_PERMISSION = 'role permission',
  USER_PERMISSION = 'user permission',
  POST = 'post',
  POST_MEDIA = 'post media',
  POST_LIKE = 'post like',
  POST_COMMENT = 'post comment',
  POST_COMMENT_LIKE = 'post comment like',
  POST_VIEW = 'post view',
  LINK_POST = 'link post',
  WAITLIST = 'waitlist',
  OPPORTUNITY = 'opportunity',
  OPPORTUNITY_ATTACHMENT = 'opportunity attachment',
  SUBTYPE = 'subtype',
  CATEGORY = 'category',
  SUBSCRIPTION = 'subscription',
  FOLLOWER = 'follower',
  EMPLOYER = 'employer',
  CONNECTION = 'connection',
  PRESTIGE_MEMBERSHIP_APPLICATION = 'prestige membership application',
  TAG = 'tag',
  TAG_FOLLOWER = 'tag follower',
  POLL_VOTE = 'poll vote',
  POLL_OPTION = 'poll option',
  STUDENT = 'student',
  SPECIALIST = 'specialist',
  ALLIED_CARDIAC = 'allied cardiac',
}
