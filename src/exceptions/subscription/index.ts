import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const accountTypeAndSubscriptionPlanNotMatching = () =>
  new CustomHttpException(
    'subscription.the_account_type_and_subscription_plan_user_chosen_is_not_matching',
    HttpStatus.BAD_REQUEST,
  );

export const professionalCategoryIsRequiredIfAccountTypeIsProfessional = () =>
  new CustomHttpException(
    'subscription.professional_category_is_required_if_account_type_is_professional',
    HttpStatus.BAD_REQUEST,
  );

export const organizationApplicationFieldsRequired = () =>
  new CustomHttpException(
    'subscription.organization_application_fields_required',
    HttpStatus.BAD_REQUEST,
  );

export const subscriptionDoesNotAllowVideosOrHighlights = () =>
  new CustomHttpException(
    'subscription.subscription_does_not_allow_videos_or_highlights',
    HttpStatus.BAD_REQUEST,
  );

export const invalidStripeSignatureException = () =>
  new CustomHttpException('subscription.invalid_signature', HttpStatus.BAD_REQUEST);

// Missing metadata in checkout session
export const missingStripeMetadataException = () =>
  new CustomHttpException('subscription.missing_metadata', HttpStatus.BAD_REQUEST);

export const stripePriceIdNotFoundException = () =>
  new CustomHttpException('subscription.stripe_price_id_not_found', HttpStatus.NOT_FOUND);
