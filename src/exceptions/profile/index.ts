import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const endYearMustBeGreaterThanStartYear = () =>
  new CustomHttpException('profile.endYearMustBeGreaterThanStartYear', HttpStatus.FORBIDDEN);

export const reviewsDisabledView = () =>
  new CustomHttpException('profile.reviews_disabled_for_viewing', HttpStatus.FORBIDDEN);

export const reviewsDisabledCreate = () =>
  new CustomHttpException('profile.reviews_disabled_for_creation', HttpStatus.FORBIDDEN);
