import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

import { DEFAULT_PUBLIC_POST_TAG } from '@/constants/posts';

export const postScheduleDateMinimum10MinutesRequired = () =>
  new CustomHttpException(
    'posts.post_schedule.minimum_10_minutes_required',
    HttpStatus.NOT_ACCEPTABLE,
  );

export const postInUnsubscribedCountryException = new CustomHttpException(
  'posts.country_id.post_in_unsubscribed_country',
  HttpStatus.NOT_ACCEPTABLE,
);

export const invalidPostStatusTransitionException = () =>
  new CustomHttpException(
    'posts.post_status.invalid_transition',
    HttpStatus.BAD_REQUEST, // Using BAD_REQUEST for status as it's more appropriate for a transition error
  );

export const postCreatorCannotRemoveOwnPost = () =>
  new CustomHttpException(
    'posts.reported_post.post_creator_cannot_report_a_post',
    HttpStatus.BAD_REQUEST,
  );

export const reportedPostIsNotModifiable = () =>
  new CustomHttpException(
    'posts.reported_post.reported_post_is_not_modifiable',
    HttpStatus.FORBIDDEN,
  );

export const audiencePermissionError = () =>
  new CustomHttpException(
    'posts.speciality_audience.invalid_speciality_audience',
    HttpStatus.BAD_REQUEST,
  );

export const commentNotAQuestionPost = () =>
  new CustomHttpException(
    'posts.question_post.comment_not_a_question_post',
    HttpStatus.BAD_REQUEST,
  );

export const commentAlreadyPinned = () =>
  new CustomHttpException('posts.question_post.comment_already_pinned', HttpStatus.BAD_REQUEST);

export const pinnedCommentNotFound = () =>
  new CustomHttpException(
    'posts.question_post.pinned_comment_not_found',
    HttpStatus.NOT_ACCEPTABLE,
  );

export const onlyTopLevelCommentCanBeUsedAsParent = () =>
  new CustomHttpException(
    'posts.comments.only_top_level_comment_can_be_used_as_parent',
    HttpStatus.BAD_REQUEST,
  );

export const mentionedUserNotFound = () =>
  new CustomHttpException('posts.comments.mentioned_user_not_found', HttpStatus.BAD_REQUEST);

export const parentCommentNotFound = () =>
  new CustomHttpException('posts.comments.parent_comment_not_found', HttpStatus.BAD_REQUEST);

export const pollExpiredException = () =>
  new CustomHttpException('posts.poll_posts.poll_expired', HttpStatus.GONE);

export const alreadyHasCustomOptionException = () =>
  new CustomHttpException('posts.poll_posts.already_has_custom_option', HttpStatus.CONFLICT);

export const cannotAddCustomOptionAfterVoteException = () =>
  new CustomHttpException(
    'posts.poll_posts.cannot_add_custom_option_after_vote',
    HttpStatus.CONFLICT,
  );

export const communityIsRequiredForWorkspacePost = () =>
  new CustomHttpException('posts.workspace_post.community_required', HttpStatus.BAD_REQUEST);

export const audienceIsRequiredForWorkspacePost = () =>
  new CustomHttpException('posts.workspace_post.audience_required', HttpStatus.BAD_REQUEST);

export const publicPostMustHaveSingleTag = (tag: string = DEFAULT_PUBLIC_POST_TAG) =>
  new CustomHttpException(
    'posts.public_post.single_public_tag_required',
    HttpStatus.BAD_REQUEST,
    { args: { tag } }, // assuming your CustomHttpException supports interpolation via context
  );

export const publicPostMustBePublicCommunity = () =>
  new CustomHttpException('posts.public_post.must_be_public_community', HttpStatus.BAD_REQUEST);

export const publicPostCannotHaveAudience = () =>
  new CustomHttpException('posts.public_post.cannot_have_audience', HttpStatus.BAD_REQUEST);

export const cannotUpdateVotedPollException = () =>
  new CustomHttpException('posts.poll_posts.cannot_update_voted_poll', HttpStatus.BAD_REQUEST);

export const cannotUpdateExpiredPollException = () =>
  new CustomHttpException('posts.poll_posts.cannot_update_expired_poll', HttpStatus.BAD_REQUEST);
