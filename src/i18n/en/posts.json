{"post_schedule": {"minimum_10_minutes_required": "The schedule time must be at least 10 minutes from now."}, "country_id": {"post_in_unsubscribed_country": "The specified country is not included in your subscription"}, "post_status": {"invalid_transition": "The requested transition for post status is not valid. Please ensure you follow the correct status flow."}, "reported_post": {"post_creator_cannot_report_a_post": "The post creator cannot report their own post", "reported_post_is_not_modifiable": "A reported post cannot be modified."}, "speciality_audience": {"invalid_speciality_audience": "You are only allowed to create posts for your speciality audience"}, "question_post": {"comment_not_a_question_post": "Comment shared to pin is not for a question post.", "comment_already_pinned": "Comment shared to pin is already pinned.", "pinned_comment_not_found": "<PERSON><PERSON><PERSON> comment not found."}, "comments": {"only_top_level_comment_can_be_used_as_parent": "Only top-level comments can be used as a parent for replies.", "mentioned_user_not_found": "The user you mentioned could not be found. They may have been removed or do not exist.", "parent_comment_not_found": "The parent comment could not be found. It may have been deleted or never existed."}, "poll_posts": {"poll_expired": "This poll has expired. You can no longer vote or interact with it.", "already_has_custom_option": "You have already added your own custom option. You must retract it before voting on another option.", "cannot_add_custom_option_after_vote": "You cannot add a new custom option unless you retract your vote from another option.", "cannot_update_voted_poll": "You cannot update a poll that already has votes.", "cannot_update_expired_poll": "This poll has already expired, so poll-specific fields cannot be updated."}, "workspace_post": {"community_required": "Community is required for workspace posts.", "audience_required": "Audience is required for workspace posts."}, "public_post": {"single_public_tag_required": "Public posts must have exactly one tag: {tag}", "must_be_public_community": "Public posts must be associated with the Public community.", "cannot_have_audience": "Public posts should not have an audience specified."}}