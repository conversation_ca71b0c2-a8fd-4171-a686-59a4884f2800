import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, timestamp, uuid, text, pgEnum } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { UserSegment } from '@/constants/user-segments';

import { workspaceSubscriptions } from './workspace-subscriptions';
import { subscriptionPlanFeatures } from './subscription-plan-features';

const userSegmentEnum = pgEnum('user_segment', enumToPgEnum(UserSegment));

export const subscriptionPlans = pgTable('subscription_plans', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  title: text('title').notNull(),
  subtitle: text('subtitle').notNull(),
  description: text('description'),
  userSegment: userSegmentEnum('user_segment').notNull(), // CARDIAC_SPECIALIST, ALLIED_CARDIAC, STUDENT, ORGANISATION
  assetUri: text('asset_uri'),
  priceMonthly: integer('price_monthly').notNull(),
  priceYearly: integer('price_yearly').notNull(),

  // IDs from Stripe
  stripePriceMonthlyId: text('stripe_price_monthly_id'),
  stripePriceYearlyId: text('stripe_price_yearly_id'),

  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const subscriptionPlansRelations = relations(subscriptionPlans, ({ many }) => ({
  workspaceSubscriptions: many(workspaceSubscriptions),
  planFeatures: many(subscriptionPlanFeatures),
}));

export type SubscriptionPlans = typeof subscriptionPlans.$inferSelect;
