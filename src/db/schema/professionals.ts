import { relations, sql } from 'drizzle-orm';
import { integer, json, pgTable, smallint, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';
import { employers } from './employers';
import { segmentCategories } from './segment-categories';

import {
  IAreaOfExpertise,
  IClinicalInterest,
  IFAQ,
  IFundraiserSection,
  IGetInTouchDetails,
  IQualificationDetailsItem,
  IShowcase,
  IWorkExperience,
} from '@/interfaces/workspaces';

export const professionals = pgTable('professionals', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, { onDelete: 'cascade' })
    .notNull(),
  title: text('title').notNull(),
  segmentCategoryId: uuid('segment_category_id')
    .references(() => segmentCategories.id, { onDelete: 'cascade' })
    .notNull(),
  qualifications: text('qualifications').notNull(),
  qualificationDetails: json('qualification_details').$type<IQualificationDetailsItem>(),
  clinicalInterests: json('clinical_interests').$type<IClinicalInterest[]>(),
  areaOfExpertises: json('area_of_expertises').$type<IAreaOfExpertise[]>(),
  getInTouch: json('get_in_touch').$type<IGetInTouchDetails>(),
  faqs: json('faqs').$type<IFAQ[]>(),
  workExperiences: json('work_experiences').$type<IWorkExperience[]>(),
  showcases: json('showcases').$type<IShowcase[]>(),
  fundraiser: json('fundraiser').$type<IFundraiserSection>(),
  jobTitleYear: smallint('job_title_year'),
  designation: text('designation').notNull(),
  employerId: uuid('employer_id').references(() => employers.id, { onDelete: 'cascade' }),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const professionalsRelations = relations(professionals, ({ one }) => ({
  workspace: one(workspaces, {
    fields: [professionals.workspaceId],
    references: [workspaces.id],
  }),
  segmentCategory: one(segmentCategories, {
    fields: [professionals.segmentCategoryId],
    references: [segmentCategories.id],
  }),
  employer: one(employers, {
    fields: [professionals.employerId],
    references: [employers.id],
  }),
}));

export type Professionals = typeof professionals.$inferSelect;
