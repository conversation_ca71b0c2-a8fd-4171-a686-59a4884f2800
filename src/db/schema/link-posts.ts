import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { posts } from './posts';

export const linkPosts = pgTable('link_posts', {
  postId: uuid('post_id')
    .primaryKey()
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  link: text('link').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const linkPostsRelations = relations(linkPosts, ({ one }) => ({
  post: one(posts, {
    fields: [linkPosts.postId],
    references: [posts.id],
  }),
}));

export type LinkPost = typeof linkPosts.$inferSelect;
export type NewLinkPost = typeof linkPosts.$inferInsert;
