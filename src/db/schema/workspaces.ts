import { relations, sql } from 'drizzle-orm';
import {
  boolean,
  integer,
  json,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { UserSegment } from '@/constants/user-segments';
import { PrimarySpeciality, WORKSPACE_RELATIONS, WorkspacesStatus } from '@/constants/workspaces';

import { ICustomSection, IHighlightItem } from '@/interfaces/workspaces';

import { workspaceUsers } from './workspace-users';
import { workspaceFollowers } from './workspace-followers';
import { opportunities } from './opportunities';
import { userRoles } from './user-roles';
import { posts } from './posts';
import { workspaceSubscriptions } from './workspace-subscriptions';
import { organisations } from './organisations';
import { specialists } from './specialists';
import { professionals } from './professionals';
import { users } from './users';
import { students } from './students';
import { workspaceDocuments } from './workspace-documents';
import { prestigeMembershipApplications } from './prestige-membership-applications';
import { workspaceMembers, workspaceMembersRelationNames } from './workspace-members';

const userSegmentEnum = pgEnum('user_segment', enumToPgEnum(UserSegment));

const primarySpecialityEnum = pgEnum('primary_speciality', enumToPgEnum(PrimarySpeciality));

export const workspaces = pgTable('workspaces', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  userSegment: userSegmentEnum('user_segment').notNull(), // CARDIAC_SPECIALIST, ALLIED_CARDIAC, STUDENT, ORGANISATION
  createdById: uuid('created_by_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  primarySpeciality: primarySpecialityEnum('primary_speciality').notNull(),
  highlights: json('highlights').$type<IHighlightItem[]>(),
  custom: json('custom').$type<ICustomSection>(),
  publicVideo: text('public_video'),
  professionalOrOrganisationVideo: text('professional_or_organisation_video'),
  isRatingAndReviewEnabled: boolean('is_rating_and_review_enabled').notNull().default(true),
  instagramLink: text('instagram_link'),
  facebookLink: text('facebook_link'),
  twitterLink: text('twitter_link'),
  linkedinLink: text('linkedin_link'),
  youtubeLink: text('youtube_link'),
  tiktokLink: text('tiktok_link'),
  status: integer('status').notNull().default(WorkspacesStatus.INACTIVE),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const workspacesRelations = relations(workspaces, ({ many, one }) => ({
  workspaceUsers: many(workspaceUsers),
  workspaceFollowers: many(workspaceFollowers),
  workspaceDocuments: many(workspaceDocuments),
  opportunities: many(opportunities),
  userRoles: many(userRoles),
  posts: many(posts),
  subscriptions: many(workspaceSubscriptions),
  createdByUser: one(users, {
    fields: [workspaces.createdById],
    references: [users.id],
  }),
  members: many(workspaceMembers, {
    relationName: workspaceMembersRelationNames.members,
  }),
  memberships: many(workspaceMembers, {
    relationName: workspaceMembersRelationNames.memberships,
  }),
  prestigeMembershipApplications: many(prestigeMembershipApplications),
  [WORKSPACE_RELATIONS.ORGANISATION]: one(organisations, {
    fields: [workspaces.id],
    references: [organisations.workspaceId],
  }),
  [WORKSPACE_RELATIONS.SPECIALISTS]: one(specialists, {
    fields: [workspaces.id],
    references: [specialists.workspaceId],
  }),
  [WORKSPACE_RELATIONS.PROFESSIONAL]: one(professionals, {
    fields: [workspaces.id],
    references: [professionals.workspaceId],
  }),
  [WORKSPACE_RELATIONS.STUDENTS]: one(students, {
    fields: [workspaces.id],
    references: [students.workspaceId],
  }),
}));

export type Workspaces = typeof workspaces.$inferSelect;
