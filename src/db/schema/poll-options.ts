import { relations, sql } from 'drizzle-orm';
import {
  boolean,
  integer,
  pgEnum,
  pgTable,
  smallint,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { EntityType } from '@/constants/user-types';

import { pollPosts } from './poll-posts';
import { pollVotes } from './poll-votes';

// EntityType enum for created_by_entity_type
const entityTypeEnum = pgEnum('created_by_entity_type', enumToPgEnum(EntityType));

export const pollOptions = pgTable('poll_options', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  pollId: uuid('poll_id')
    .notNull()
    .references(() => pollPosts.postId, { onDelete: 'cascade' }),
  text: text('text').notNull(),
  isCustom: boolean('is_custom').notNull().default(false),
  createdByEntityId: uuid('created_by_entity_id'), // nullable
  createdByEntityType: entityTypeEnum('created_by_entity_type'), // nullable
  order: smallint('order').notNull(),
  status: integer('status').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const pollOptionsRelations = relations(pollOptions, ({ one, many }) => ({
  poll: one(pollPosts, {
    fields: [pollOptions.pollId],
    references: [pollPosts.postId],
  }),
  votes: many(pollVotes),
}));

export type PollOption = typeof pollOptions.$inferSelect;
export type NewPollOption = typeof pollOptions.$inferInsert;
