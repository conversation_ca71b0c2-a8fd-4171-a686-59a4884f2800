import { pgTable, uuid, timestamp, integer, text, pgEnum } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';

import { enumToPgEnum } from '@/utils/database';

import { EntityType } from '@/constants/user-types';

import { posts } from './posts';
import { postNotifications } from './post-notifications';
import { questionPosts } from './question-posts';
import { postCommentLikes } from './post-comment-likes';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const postComments = pgTable('post_comments', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  entityId: uuid('entity_id').notNull(),
  entityType: entityTypeEnum('entity_type').notNull(),
  postId: uuid('post_id')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  parentCommentId: uuid('parent_comment_id').references((): any => postComments.id, {
    onDelete: 'cascade',
  }),
  comment: text('comment').notNull(),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at', { mode: 'string' }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Define the relations
export const postCommentsRelations = relations(postComments, ({ one, many }) => ({
  post: one(posts, {
    fields: [postComments.postId],
    references: [posts.id],
  }),
  parentComment: one(postComments, {
    fields: [postComments.parentCommentId],
    references: [postComments.id],
  }),
  postNotifications: many(postNotifications),
  questionPost: one(questionPosts, {
    fields: [postComments.id],
    references: [questionPosts.pinnedCommentId],
  }),
  likes: many(postCommentLikes),
}));

export type PostComments = typeof postComments.$inferSelect;
export type NewPostComments = typeof postComments.$inferInsert;
