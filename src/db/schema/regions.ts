import { pgTable, uuid, text, timestamp, doublePrecision } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';
import { locations } from './locations';

let regionsTable: ReturnType<typeof pgTable>;

export const regions = (regionsTable = pgTable('regions', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),

  name: text('name').notNull(),
  type: text('type').notNull(), // 'state' | 'country' | 'continent'
  latitude: doublePrecision('latitude'),
  longitude: doublePrecision('longitude'),
  mapboxId: text('mapbox_id'),

  parentId: uuid('parent_id').references(() => regionsTable.id, { onDelete: 'cascade' }),

  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
}));

export const regionRelations = relations(regions, ({ one, many }) => ({
  parent: one(regions, {
    fields: [regions.parentId],
    references: [regions.id],
  }),
  children: many(regions),
  locations: many(locations),
}));

export type NewRegion = typeof regions.$inferInsert;
