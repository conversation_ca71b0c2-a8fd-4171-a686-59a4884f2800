import { relations, sql } from 'drizzle-orm';
import { integer, json, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';

import {
  IAreaOfExpertise,
  IClinicalInterest,
  IQualificationDetailsItem,
  IWorkExperience,
} from '@/interfaces/workspaces';

export const students = pgTable('students', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, { onDelete: 'cascade' })
    .notNull(),
  institutionName: text('institution_name').notNull(),
  qualificationDetails: json('qualification_details').$type<IQualificationDetailsItem>(),
  workExperiences: json('work_experiences').$type<IWorkExperience[]>(),
  clinicalInterests: json('clinical_interests').$type<IClinicalInterest[]>(),
  areaOfExpertises: json('area_of_expertises').$type<IAreaOfExpertise[]>(),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const studentsRelations = relations(students, ({ one }) => ({
  workspace: one(workspaces, {
    fields: [students.workspaceId],
    references: [workspaces.id],
  }),
}));

export type Students = typeof students.$inferSelect;
