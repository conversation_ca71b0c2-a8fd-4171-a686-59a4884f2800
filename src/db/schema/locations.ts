import { pgTable, uuid, text, timestamp, doublePrecision } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';

import { regions } from './regions';
import { organisations } from './organisations';

export const locations = pgTable('locations', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),

  placeId: text('place_id').unique(),
  name: text('name').notNull(),
  latitude: doublePrecision('latitude').notNull(),
  longitude: doublePrecision('longitude').notNull(),
  regionId: uuid('region_id').references(() => regions.id, { onDelete: 'cascade' }),

  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const locationRelations = relations(locations, ({ one, many }) => ({
  region: one(regions, {
    fields: [locations.regionId],
    references: [regions.id],
  }),
  organisations: many(organisations),
}));

export type NewLocation = typeof locations.$inferInsert;
