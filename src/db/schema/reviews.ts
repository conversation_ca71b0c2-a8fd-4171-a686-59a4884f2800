import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, smallint, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';

import { enumToPgEnum } from '@/utils/database';

import { EntityType } from '@/constants/user-types';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const reviews = pgTable('reviews', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  message: text('message').notNull(),
  rating: smallint('rating').notNull(),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, { onDelete: 'cascade' })
    .notNull(),
  entityType: entityTypeEnum('entity_type').notNull(),
  entityId: uuid('entity_id').notNull(),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const reviewsRelations = relations(reviews, ({ one }) => ({
  workspace: one(workspaces, {
    fields: [reviews.workspaceId],
    references: [workspaces.id],
  }),
}));

export type Review = typeof reviews.$inferSelect;
