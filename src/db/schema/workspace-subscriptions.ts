import { relations, sql } from 'drizzle-orm';
import { boolean, date, integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';
import { workspaceSubscriptionCountries } from './workspace-subscription-countries';
import { subscriptionPlans } from './subscription-plans';

export const workspaceSubscriptions = pgTable('workspace_subscriptions', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, { onDelete: 'cascade' }),
  subscriptionPlanId: uuid('subscription_plan_id')
    .notNull()
    .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
  isYearly: boolean('is_yearly').notNull(),
  startDate: date('start_date').notNull(),
  endDate: date('end_date').notNull(),
  paymentDate: date('payment_date').notNull(),
  paymentMethod: text('payment_method').notNull(),
  status: integer('status').notNull().default(1),
  transactionId: text('transaction_id'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const subscriptionWorkspacesRelations = relations(
  workspaceSubscriptions,
  ({ one, many }) => ({
    workspace: one(workspaces, {
      fields: [workspaceSubscriptions.workspaceId],
      references: [workspaces.id],
    }),
    subscription: one(subscriptionPlans, {
      fields: [workspaceSubscriptions.subscriptionPlanId],
      references: [subscriptionPlans.id],
    }),
    workspaceSubscriptionCountries: many(workspaceSubscriptionCountries),
  }),
);

export type NewWorkspaceSubscription = typeof workspaceSubscriptions.$inferInsert;
