import { relations } from 'drizzle-orm';
import { boolean, customType, integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { posts } from './posts';
import { pollOptions } from './poll-options';
import { pollVotes } from './poll-votes';

export const customTimestamp = customType<{
  data: Date;
  driverData: string;
}>({
  dataType() {
    return 'timestamp with time zone'; // or 'timestamp' if you use without time zone
  },
  fromDriver(value) {
    return new Date(value); // always ensures you get a JS Date
  },
  toDriver(value) {
    return value instanceof Date ? value.toISOString() : value;
  },
});

export const pollPosts = pgTable('poll_posts', {
  postId: uuid('post_id')
    .primaryKey()
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  question: text('question').notNull(),
  durationDays: integer('duration_days').notNull(),
  expiresAt: customTimestamp('expires_at').notNull(),
  allowCustomAnswer: boolean('allow_custom_answer').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Relations
export const pollPostsRelations = relations(pollPosts, ({ one, many }) => ({
  post: one(posts, {
    fields: [pollPosts.postId],
    references: [posts.id],
  }),
  options: many(pollOptions),
  votes: many(pollVotes),
}));

export type PollPost = typeof pollPosts.$inferSelect;
export type NewPollPost = typeof pollPosts.$inferInsert;
