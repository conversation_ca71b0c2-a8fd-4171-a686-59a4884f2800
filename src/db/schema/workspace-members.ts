import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';

export const workspaceMembersRelationNames = {
  members: 'members', // workspaces this one has as members
  memberships: 'memberships', // workspaces this one belongs to
};

export const workspaceMembers = pgTable('workspace_members', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),

  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, { onDelete: 'cascade' }),

  memberWorkspaceId: uuid('member_workspace_id')
    .notNull()
    .references(() => workspaces.id, { onDelete: 'cascade' }),

  status: integer('status').notNull(), // e.g. pending / accepted enum

  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const workspaceMembersRelations = relations(workspaceMembers, ({ one }) => ({
  // workspace that owns the members
  workspace: one(workspaces, {
    fields: [workspaceMembers.workspaceId],
    references: [workspaces.id],
    relationName: workspaceMembersRelationNames.members,
  }),

  // workspace that is a member of the above
  memberWorkspace: one(workspaces, {
    fields: [workspaceMembers.memberWorkspaceId],
    references: [workspaces.id],
    relationName: workspaceMembersRelationNames.memberships,
  }),
}));

export type NewWorkspaceMember = typeof workspaceMembers.$inferInsert;
