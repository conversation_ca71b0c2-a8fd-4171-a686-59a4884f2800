import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { EntityType } from '@/constants/user-types';

import { pollPosts } from './poll-posts';
import { pollOptions } from './poll-options';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const pollVotes = pgTable('poll_votes', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  pollId: uuid('poll_id')
    .notNull()
    .references(() => pollPosts.postId, { onDelete: 'cascade' }),
  entityType: entityTypeEnum('entity_type').notNull(),
  entityId: uuid('entity_id').notNull(),
  optionId: uuid('option_id')
    .notNull()
    .references(() => pollOptions.id, { onDelete: 'cascade' }),
  status: integer('status').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const pollVotesRelations = relations(pollVotes, ({ one }) => ({
  poll: one(pollPosts, {
    fields: [pollVotes.pollId],
    references: [pollPosts.postId],
  }),
  option: one(pollOptions, {
    fields: [pollVotes.optionId],
    references: [pollOptions.id],
  }),
}));

export type PollVote = typeof pollVotes.$inferSelect;
export type NewPollVote = typeof pollVotes.$inferInsert;
