import { relations, sql } from 'drizzle-orm';
import { boolean, integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';

export const workspaceInvitedUsers = pgTable('workspace_invited_users', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),

  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, { onDelete: 'cascade' }),

  email: text('email').notNull(),

  tempName: text('temp_name'),
  tempRole: text('temp_role'),

  status: integer('status').notNull(),

  autoConnectOnAccept: boolean('auto_connect_on_accept').notNull(),

  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const workspaceInvitedUsersRelations = relations(workspaceInvitedUsers, ({ one }) => ({
  workspace: one(workspaces, {
    fields: [workspaceInvitedUsers.workspaceId],
    references: [workspaces.id],
  }),
}));

export type NewWorkspaceInvitedUser = typeof workspaceInvitedUsers.$inferInsert;
