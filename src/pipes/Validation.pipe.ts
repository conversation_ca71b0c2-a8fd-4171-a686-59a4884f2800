/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-function-type */
import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import capitalize from 'lodash.capitalize';

import * as systemExceptions from '@/exceptions/system';

@Injectable()
class ValidationPipe implements PipeTransform<any> {
  async transform(value: any, { metatype }: ArgumentMetadata) {
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }

    const object = plainToInstance(metatype, value);
    const errors = await validate(object);

    if (errors.length > 0) {
      const errorMessage = this.extractErrorMessage(errors);
      throw systemExceptions.validationError(errorMessage ? capitalize(errorMessage) : undefined, {
        metadata: errors,
      });
    }

    return value;
  }

  private extractErrorMessage(errors: ValidationError[]): string | undefined {
    // Helper function to recursively find the first constraint message
    const findFirstConstraint = (error: ValidationError): string | undefined => {
      // If this error has direct constraints, return the first one
      if (error.constraints && Object.keys(error.constraints).length > 0) {
        return Object.values(error.constraints)[0];
      }

      // If this error has children, recursively search them
      if (error.children && error.children.length > 0) {
        for (const child of error.children) {
          const childMessage = findFirstConstraint(child);
          if (childMessage) {
            return childMessage;
          }
        }
      }

      return undefined;
    };

    // Try to extract error message from the first error
    const firstError = errors[0];
    if (firstError) {
      return findFirstConstraint(firstError);
    }

    return undefined;
  }

  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }
}

export default ValidationPipe;
