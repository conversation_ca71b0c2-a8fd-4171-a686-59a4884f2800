import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Add Stripe price IDs to subscription_plans
  await knex.schema.alterTable('subscription_plans', (table) => {
    table.text('stripe_price_monthly_id').nullable();
    table.text('stripe_price_yearly_id').nullable();
  });

  // Add transaction_id to workspace_subscriptions
  await knex.schema.alterTable('workspace_subscriptions', (table) => {
    table.text('transaction_id').nullable();
  });

  // Primary
  await knex('subscription_plans').where({ title: 'Primary' }).update({
    stripe_price_monthly_id: 'price_1S2Gq93pqo9W9PpXOiKUFpLS',
    stripe_price_yearly_id: 'price_1S2NZu3pqo9W9PpXIBM8lScP',
  });

  // Premium
  await knex('subscription_plans').where({ title: 'Premium' }).update({
    stripe_price_monthly_id: 'price_1S2GtD3pqo9W9PpXz2dgOApj',
    stripe_price_yearly_id: 'price_1S2NZD3pqo9W9PpXlStqOYnf',
  });
}

export async function down(knex: Knex): Promise<void> {
  // Remove added columns
  await knex.schema.alterTable('subscription_plans', (table) => {
    table.dropColumn('stripe_price_monthly_id');
    table.dropColumn('stripe_price_yearly_id');
  });

  await knex.schema.alterTable('workspace_subscriptions', (table) => {
    table.dropColumn('transaction_id');
  });
}
