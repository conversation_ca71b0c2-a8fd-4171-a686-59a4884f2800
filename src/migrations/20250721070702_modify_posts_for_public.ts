import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Make workspace_id, audience nullable
  await knex.schema.alterTable('posts', (table) => {
    table.uuid('workspace_id').nullable().alter();
    table.text('audience').nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  // First delete all posts where workspace_id is null
  await knex('posts').whereNull('workspace_id').del();

  // Then alter the columns to be non-nullable
  await knex.schema.alterTable('posts', (table) => {
    table.uuid('workspace_id').notNullable().alter();
    table.text('audience').notNullable().alter();
  });
}
