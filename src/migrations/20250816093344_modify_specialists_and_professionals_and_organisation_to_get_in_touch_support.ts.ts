import type { Knex } from 'knex';

const TABLES = ['organisations', 'professionals', 'specialists'];

const GET_IN_TOUCH_COL = 'get_in_touch';

export async function up(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.json(GET_IN_TOUCH_COL);
    });
  }
}

export async function down(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.dropColumn(GET_IN_TOUCH_COL);
    });
  }
}
