import type { Knex } from 'knex';

const ORGANISATIONS_TABLE = 'organisations';
const PROFESSIONALS_TABLE = 'professionals';
const SPECIALISTS_TABLE = 'specialists';
const STUDENTS_TABLE = 'students';

const CLINICAL_INTERESTS_COL = 'clinical_interests';
const AREA_OF_EXPERTISES_COL = 'area_of_expertises';
const FAQ_COL = 'faqs';
const SERVICES_COL = 'services';

const COL_WITH_SEGMENT = {
  [ORGANISATIONS_TABLE]: [FAQ_COL, SERVICES_COL],
  [PROFESSIONALS_TABLE]: [CLINICAL_INTERESTS_COL, AREA_OF_EXPERTISES_COL, FAQ_COL],
  [SPECIALISTS_TABLE]: [CLINICAL_INTERESTS_COL, AREA_OF_EXPERTISES_COL, FAQ_COL],
  [STUDENTS_TABLE]: [CLINICAL_INTERESTS_COL, AREA_OF_EXPERTISES_COL],
};

export async function up(knex: Knex): Promise<void> {
  for (const table of Object.keys(COL_WITH_SEGMENT)) {
    for (const col of COL_WITH_SEGMENT[table as keyof typeof COL_WITH_SEGMENT]) {
      await knex.schema.alterTable(table, (t) => {
        t.json(col);
      });
    }
  }
}

export async function down(knex: Knex): Promise<void> {
  for (const table of Object.keys(COL_WITH_SEGMENT)) {
    for (const col of COL_WITH_SEGMENT[table as keyof typeof COL_WITH_SEGMENT]) {
      await knex.schema.alterTable(table, (t) => {
        t.dropColumn(col);
      });
    }
  }
}
