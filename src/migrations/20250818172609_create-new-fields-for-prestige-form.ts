import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // organisations table
  await knex.schema.alterTable('organisations', (t) => {
    t.text('point_of_contact_email');
  });
}

export async function down(knex: Knex): Promise<void> {
  // organisations table
  await knex.schema.alterTable('organisations', (t) => {
    t.dropColumn('point_of_contact_email');
  });
}
