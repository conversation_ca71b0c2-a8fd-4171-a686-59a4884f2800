import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('posts', (t) => {
    t.uuid('original_post_id')
      .references('id')
      .inTable('posts')
      .onDelete('CASCADE')
      .index('index_posts_on_original_post_id');

    t.json('quoted_captions');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('posts', (t) => {
    t.dropColumn('original_post_id');
    t.dropColumn('quoted_captions');
  });
}
