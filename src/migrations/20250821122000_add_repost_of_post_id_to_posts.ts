import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('posts', (t) => {
    t.uuid('repost_of_post_id')
      .references('id')
      .inTable('posts')
      .onDelete('CASCADE')
      .index('index_posts_on_repost_of_post_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('posts', (t) => {
    t.dropColumn('repost_of_post_id');
  });
}
