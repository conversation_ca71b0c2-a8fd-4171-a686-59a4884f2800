import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('reviews', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('message').notNullable();
      t.smallint('rating').notNullable();
      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_reviews_on_workspace_id');
      t.text('entity_type').notNullable();
      t.uuid('entity_id').notNullable().index('index_reviews_on_entity_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
      t.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());
    })
    .raw(
      `
CREATE TRIGGER reviews_updated_at BEFORE UPDATE
ON reviews FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('reviews');
}
