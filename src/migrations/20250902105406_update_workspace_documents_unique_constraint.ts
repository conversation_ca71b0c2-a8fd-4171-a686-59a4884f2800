import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspace_documents', (t) => {
    // Drop the old unique constraint
    t.dropUnique(['document_type_id', 'sort_order']);

    // Add the new unique constraint
    t.unique(
      ['workspace_id', 'document_type_id', 'sort_order'],
      'workspace_documents_workspace_doc_sort_unique',
    );
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspace_documents', (t) => {
    // Drop the new unique constraint
    t.dropUnique(
      ['workspace_id', 'document_type_id', 'sort_order'],
      'workspace_documents_workspace_doc_sort_unique',
    );

    // Restore the old unique constraint
    t.unique(['document_type_id', 'sort_order']);
  });
}
