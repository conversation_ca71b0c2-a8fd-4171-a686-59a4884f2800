import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create regions table
  await knex.schema
    .createTable('regions', (t) => {
      t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));

      t.text('name').notNullable();
      t.text('type').notNullable(); // 'state' | 'country' | 'continent'
      t.specificType('latitude', 'double precision');
      t.specificType('longitude', 'double precision');
      t.text('mapbox_id');

      // Parent region FK
      t.uuid('parent_id')
        .references('id')
        .inTable('regions')
        .onDelete('CASCADE')
        .index('index_regions_on_parent_id');

      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

      t.unique(['name', 'type', 'parent_id']);
    })
    .raw(
      `
CREATE TRIGGER regions_updated_at
BEFORE UPDATE ON regions
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
`,
    );

  // Create locations table
  await knex.schema
    .createTable('locations', (t) => {
      t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));

      t.text('place_id').notNullable().unique();
      t.text('name').notNullable();
      t.specificType('latitude', 'double precision').notNullable();
      t.specificType('longitude', 'double precision').notNullable();

      // Region FK
      t.uuid('region_id')
        .references('id')
        .inTable('regions')
        .onDelete('CASCADE')
        .index('index_locations_on_region_id');

      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER locations_updated_at
BEFORE UPDATE ON locations
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
`,
    );

  // Alter organisations
  await knex.schema.alterTable('organisations', (t) => {
    t.uuid('location_id')
      .references('id')
      .inTable('locations')
      .onDelete('CASCADE')
      .index('index_organisations_on_location_id');

    // Drop old columns
    t.dropColumn('city');
    t.dropColumn('zip_code');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('organisations', (t) => {
    t.dropForeign(['location_id']);
    t.dropColumn('location_id');

    // Restore old columns
    t.text('city');
    t.text('zip_code');
  });

  // Drop children first
  await knex.schema.dropTableIfExists('locations');
  await knex.schema.dropTableIfExists('regions');
}
