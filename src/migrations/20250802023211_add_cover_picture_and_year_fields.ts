import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (t) => {
    t.text('cover_picture_url');
  });

  await knex.schema.alterTable('professionals', (t) => {
    t.smallint('job_title_year');
  });

  await knex.schema.alterTable('specialists', (t) => {
    t.smallint('job_title_year');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (t) => {
    t.dropColumn('cover_picture_url');
  });

  await knex.schema.alterTable('professionals', (t) => {
    t.dropColumn('job_title_year');
  });

  await knex.schema.alterTable('specialists', (t) => {
    t.dropColumn('job_title_year');
  });
}
