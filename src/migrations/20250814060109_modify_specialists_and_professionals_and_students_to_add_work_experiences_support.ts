import type { Knex } from 'knex';

const TABLES = ['students', 'professionals', 'specialists'];

const WORK_EXPERIENCES_COL = 'work_experiences';

export async function up(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.json(WORK_EXPERIENCES_COL);
    });
  }
}

export async function down(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.dropColumn(WORK_EXPERIENCES_COL);
    });
  }
}
