import type { Knex } from 'knex';
import { UserSegment } from '@/constants/user-segments';

export async function up(knex: Knex): Promise<void> {
  // Clean up existing data
  await knex('document_requirements').del();
  await knex('document_types').del();

  // Define updated document types
  const documentTypes = [
    {
      name: 'Photo ID',
      description: 'Government-issued photo ID (e.g., passport, driver’s license)',
      status: 1,
    },
    { name: 'Student ID', description: 'Valid student ID card', status: 1 },
    { name: 'Primary Degree', description: 'Primary medical/professional degree', status: 1 },
    {
      name: 'Highest Professional Certifications',
      description: 'Topmost qualification for your role',
      status: 1,
    },
    {
      name: 'Professional Registration',
      description: 'Certificate of registration/licence to practise',
      status: 1,
    },
    {
      name: 'Legal Registration Certificate',
      description: 'Company registration certificate',
      status: 1,
    },
    {
      name: 'Proof of Legal Representative',
      description: 'Proof that confirms legal authority of representative',
      status: 1,
    },
  ];

  const insertedDocumentTypes = await knex('document_types')
    .insert(documentTypes)
    .returning(['id', 'name']);

  const documentTypeMap: Record<string, string> = {};
  insertedDocumentTypes.forEach((doc) => {
    documentTypeMap[doc.name] = doc.id;
  });

  const documentRequirements = [
    // STUDENTS
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: documentTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: 'Upload a valid government-issued photo ID (passport, driver’s license, etc)',
      status: 1,
    },
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: documentTypeMap['Student ID'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload your student ID card',
      status: 1,
    },
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: documentTypeMap['Primary Degree'],
      is_required: false,
      sort_order: 3,
      max_count: 1,
      instructions: 'Upload your primary degree certificate (optional)',
      status: 1,
    },

    // CARDIOLOGISTS / SURGEONS
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: documentTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: 'Upload a valid government-issued photo ID',
      status: 1,
    },
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: documentTypeMap['Highest Professional Certifications'],
      is_required: true,
      sort_order: 2,
      max_count: 2,
      instructions: 'Your highest professional degree that certifies you for your role',
      status: 1,
    },
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: documentTypeMap['Professional Registration'],
      is_required: false,
      sort_order: 3,
      max_count: 1,
      instructions: 'Optional until CRM',
      status: 1,
    },

    // ALLIED CARDIAC
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: documentTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: 'Any government issued photo ID, such as your passport, driver’s license, etc',
      status: 1,
    },
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: documentTypeMap['Highest Professional Certifications'],
      is_required: true,
      sort_order: 2,
      max_count: 2,
      instructions: 'Your highest current professional degree that certifies you for your role',
      status: 1,
    },
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: documentTypeMap['Professional Registration'],
      is_required: false,
      sort_order: 3,
      max_count: 1,
      instructions: 'Certification of registration and licence to practise',
      status: 1,
    },

    // ORGANISATIONS
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: documentTypeMap['Legal Registration Certificate'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: 'Certification of company registration',
      status: 1,
    },
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: documentTypeMap['Proof of Legal Representative'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions:
        'Official document confirming the name of Owner, CEO, or Managing Director e.g. Official letter from the company, tax letter, bank statement',
      status: 1,
    },
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: documentTypeMap['Photo ID'],
      is_required: true,
      sort_order: 3,
      max_count: 1,
      instructions: 'Government issued Photo ID for the Owner, CEO, or Managing Director',
      status: 1,
    },
  ];

  await knex('document_requirements').insert(documentRequirements);
}

export async function down(knex: Knex): Promise<void> {
  // First, delete all new data
  await knex('document_requirements').del();
  await knex('document_types').del();

  // Restore old seeding logic from the previous migration
  const oldDocumentTypes = [
    { name: 'Photo ID', description: 'Government-issued photo identification', status: 1 },
    { name: 'Primary Degree', description: 'Primary medical or professional degree', status: 1 },
    { name: 'Academic Degree', description: 'Additional academic qualifications', status: 1 },
    {
      name: 'Professional Certification',
      description: 'Professional certifications and licenses',
      status: 1,
    },
    { name: 'Student ID', description: 'Valid student identification card', status: 1 },
    {
      name: 'Bonafide Certificate',
      description: 'Certificate from educational institution',
      status: 1,
    },
  ];

  const insertedOldTypes = await knex('document_types')
    .insert(oldDocumentTypes)
    .returning(['id', 'name']);

  const oldTypeMap: Record<string, string> = {};
  insertedOldTypes.forEach((type) => {
    oldTypeMap[type.name] = type.id;
  });

  const oldDocumentRequirements = [
    // CARDIAC_SPECIALIST
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: oldTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: "Upload a valid government-issued photo ID (passport, driver's license, etc.)",
      status: 1,
    },
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: oldTypeMap['Primary Degree'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload your primary medical degree certificate',
      status: 1,
    },
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: oldTypeMap['Academic Degree'],
      is_required: false,
      sort_order: 3,
      max_count: 3,
      instructions: 'Upload any additional academic qualifications (optional)',
      status: 1,
    },
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: oldTypeMap['Professional Certification'],
      is_required: true,
      sort_order: 4,
      max_count: 3,
      instructions: 'Upload your professional certifications or licenses',
      status: 1,
    },

    // ALLIED_CARDIAC
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: oldTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: "Upload a valid government-issued photo ID (passport, driver's license, etc.)",
      status: 1,
    },
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: oldTypeMap['Primary Degree'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload your primary professional degree certificate',
      status: 1,
    },
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: oldTypeMap['Academic Degree'],
      is_required: false,
      sort_order: 3,
      max_count: 3,
      instructions: 'Upload any additional academic qualifications (optional)',
      status: 1,
    },
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: oldTypeMap['Professional Certification'],
      is_required: true,
      sort_order: 4,
      max_count: 3,
      instructions: 'Upload your professional certifications or licenses',
      status: 1,
    },

    // ORGANISATION
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: oldTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: 'Upload a valid government-issued photo ID of the authorized representative',
      status: 1,
    },
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: oldTypeMap['Primary Degree'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload organization registration certificate or equivalent document',
      status: 1,
    },
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: oldTypeMap['Academic Degree'],
      is_required: false,
      sort_order: 3,
      max_count: 2,
      instructions: 'Upload any additional organizational credentials (optional)',
      status: 1,
    },
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: oldTypeMap['Professional Certification'],
      is_required: true,
      sort_order: 4,
      max_count: 3,
      instructions: 'Upload organizational certifications or accreditations',
      status: 1,
    },

    // STUDENT
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: oldTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: "Upload a valid government-issued photo ID (passport, driver's license, etc.)",
      status: 1,
    },
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: oldTypeMap['Student ID'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload your current student ID card',
      status: 1,
    },
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: oldTypeMap['Bonafide Certificate'],
      is_required: true,
      sort_order: 3,
      max_count: 1,
      instructions: 'Upload a bonafide certificate from your educational institution',
      status: 1,
    },
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: oldTypeMap['Primary Degree'],
      is_required: false,
      sort_order: 4,
      max_count: 1,
      instructions: 'Upload any previous degree certificates (optional)',
      status: 1,
    },
  ];

  await knex('document_requirements').insert(oldDocumentRequirements);
}
