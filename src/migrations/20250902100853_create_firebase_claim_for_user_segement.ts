import type { K<PERSON> } from 'knex';
import { getAuth, UserRecord } from 'firebase-admin/auth';
import { initializeApp, cert, getApps } from 'firebase-admin/app';

import envConfiguration from '@/config/configuration';
import { AuthConstants } from '@/constants/auth';

const envConfig = envConfiguration();

if (!getApps().length) {
  initializeApp({
    credential: cert(envConfig.firebaseServiceAccount),
  });
}

export async function up(knex: Knex): Promise<void> {
  const trx = await knex.transaction();

  // Backup for rollback
  const originalClaimsMap = new Map<string, any>();

  try {
    const usersWithSegments = await trx('users')
      .select('users.provider_id', 'roles.key as role_key')
      .join('user_roles', 'user_roles.user_id', 'users.id')
      .join('roles', 'roles.id', 'user_roles.role_id')
      .whereIn('roles.key', ['ORGANISATION', 'CARDIAC_SPECIALIST', 'ALLIED_CARDIAC', 'STUDENT']);

    for (const user of usersWithSegments) {
      try {
        const userRecord: UserRecord = await getAuth().getUser(user.provider_id);
        const currentClaims = userRecord.customClaims || {};

        // backup
        originalClaimsMap.set(user.provider_id, { ...currentClaims });

        // set new claim
        const updatedClaims = {
          ...currentClaims,
          [AuthConstants.FIREBASE_CLAIM_USER_SEGMENT]: user.role_key,
        };

        await getAuth().setCustomUserClaims(user.provider_id, updatedClaims);
      } catch (err) {
        // rollback already updated users
        for (const [providerId, originalClaims] of originalClaimsMap.entries()) {
          try {
            await getAuth().setCustomUserClaims(providerId, originalClaims);
          } catch (rollbackError) {
            console.error(`Failed to rollback user ${providerId}`, rollbackError);
          }
        }
        throw err; // fail migration
      }
    }

    await trx.commit();
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  const trx = await knex.transaction();

  try {
    const users = await trx('users').select('provider_id');

    for (const user of users) {
      try {
        await removeUserSegmentClaim(user.provider_id);
      } catch (err) {
        throw err;
      }
    }

    await trx.commit();
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}

export async function removeUserSegmentClaim(providerId: string) {
  const userRecord: UserRecord = await getAuth().getUser(providerId);
  const currentClaims = userRecord.customClaims || {};

  // Clone claims and remove the userSegment claim
  const remainingClaims = { ...currentClaims };
  delete remainingClaims[AuthConstants.FIREBASE_CLAIM_USER_SEGMENT];

  await getAuth().setCustomUserClaims(providerId, remainingClaims);
}
