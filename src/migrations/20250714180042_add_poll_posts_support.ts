import { EntityType } from '@/constants/user-types';
import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.dropTable('post_polls');

  // poll_posts table
  await knex.schema
    .createTable('poll_posts', (table) => {
      table
        .uuid('post_id', { primaryKey: true })
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_poll_posts_on_post_id');
      table.text('question').notNullable();
      table.integer('duration_days').notNullable();
      table.timestamp('expires_at', { useTz: true }).notNullable();
      table.boolean('allow_custom_answer').notNullable().defaultTo(false);
      table.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER poll_posts_updated_at BEFORE UPDATE
ON poll_posts FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );

  // poll_options table
  await knex.schema
    .createTable('poll_options', (table) => {
      table.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      table
        .uuid('poll_id')
        .notNullable()
        .references('post_id')
        .inTable('poll_posts')
        .onDelete('CASCADE')
        .index('index_poll_options_on_poll_id');
      table.text('text').notNullable();
      table.boolean('is_custom').notNullable().defaultTo(false);
      table.uuid('created_by_entity_id').index('index_poll_options_on_created_by_entity_id'); // nullable, can be user or workspace
      table.text('created_by_entity_type'); // nullable, 'USER' or 'WORKSPACE'
      table.smallint('order').notNullable();
      table.integer('status').notNullable();
      table.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER poll_options_updated_at BEFORE UPDATE
ON poll_options FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );

  // poll_votes table
  await knex.schema.createTable('poll_votes', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
    table
      .uuid('poll_id')
      .notNullable()
      .references('post_id')
      .inTable('poll_posts')
      .onDelete('CASCADE')
      .index('index_poll_votes_on_poll_id');
    table.text('entity_type').notNullable();
    table.uuid('entity_id').notNullable().index('index_poll_votes_on_entity_id');
    table
      .uuid('option_id')
      .notNullable()
      .references('id')
      .inTable('poll_options')
      .onDelete('CASCADE')
      .index('index_poll_votes_on_option_id');
    table.integer('status').notNullable();
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    table.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());
  }).raw(`
  CREATE TRIGGER poll_votes_updated_at
  BEFORE UPDATE ON poll_votes
  FOR EACH ROW
  EXECUTE PROCEDURE update_updated_at_column();
`);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('poll_votes');
  await knex.schema.dropTableIfExists('poll_options');
  await knex.schema.dropTableIfExists('poll_posts');

  await knex.schema
    .createTable('post_polls', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));

      t.uuid('post_id')
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_post_polls_on_post_id');

      t.text('option').notNullable();

      t.uuid('entity_id').notNullable().index('index_post_polls_on_entity_id');

      t.text('entity_type').notNullable().defaultTo(EntityType.USER);

      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
  CREATE TRIGGER post_polls_updated_at BEFORE UPDATE 
  ON post_polls FOR EACH ROW EXECUTE PROCEDURE 
  update_updated_at_column();
  `,
    );
}
