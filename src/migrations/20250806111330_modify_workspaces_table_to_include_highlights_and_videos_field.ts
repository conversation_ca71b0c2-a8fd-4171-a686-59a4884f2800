import type { Knex } from 'knex';

const WORKSPACES_TABLE = 'workspaces';

const HIGHLIGHTS_COL = 'highlights';
const PUBLIC_VIDEO_COL = 'public_video';
const PROFESSIONAL_OR_ORGANISATION_COL = 'professional_or_organisation_video';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(WORKSPACES_TABLE, (table) => {
    table.json(HIGHLIGHTS_COL);
    table.text(PUBLIC_VIDEO_COL);
    table.text(PROFESSIONAL_OR_ORGANISATION_COL);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(WORKSPACES_TABLE, (table) => {
    table.dropColumn(HIGHLIGHTS_COL);
    table.dropColumn(PUBLIC_VIDEO_COL);
    table.dropColumn(PROFESSIONAL_OR_ORGANISATION_COL);
  });
}
