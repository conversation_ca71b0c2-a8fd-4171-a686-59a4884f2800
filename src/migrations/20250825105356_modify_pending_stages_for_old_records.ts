import type { Knex } from 'knex';
import { getAuth, UserRecord } from 'firebase-admin/auth';
import { initializeApp, cert, getApps } from 'firebase-admin/app';

import envConfiguration from '@/config/configuration';
import { AuthConstants } from '@/constants/auth';
import { AccountSetupStage } from '@/constants/users';

const envConfig = envConfiguration();

if (!getApps().length) {
  initializeApp({
    credential: cert(envConfig.firebaseServiceAccount),
  });
}

export async function up(knex: Knex): Promise<void> {
  const trx = await knex.transaction();

  // To store original claims for rollback
  const originalClaimsMap = new Map<string, any>();

  try {
    const users = await trx('users')
      .select('provider_id', 'current_stage')
      .whereNotNull('provider_id')
      .whereNot('current_stage', AccountSetupStage.COMPLETED);

    for (const user of users) {
      try {
        const userRecord = await getAuth().getUser(user.provider_id);
        const currentClaims = userRecord.customClaims || {};

        // Backup current claims
        originalClaimsMap.set(user.provider_id, { ...currentClaims });

        const updatedClaims = {
          ...currentClaims,
          [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: getPendingStages(user.current_stage),
        };

        await getAuth().setCustomUserClaims(user.provider_id, updatedClaims);
      } catch (err) {
        // Rollback all changes on Firebase
        for (const [providerId, originalClaims] of originalClaimsMap.entries()) {
          try {
            await getAuth().setCustomUserClaims(providerId, originalClaims);
          } catch (rollbackError) {
            console.error(`Failed to rollback user ${providerId}`, rollbackError);
          }
        }

        throw err; // Forcing the migration fail error
      }
    }

    await trx.commit();
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  const trx = await knex.transaction();

  try {
    const users = await trx('users').select('provider_id').whereNotNull('provider_id');

    for (const user of users) {
      try {
        await updateFirebaseClaims(user.provider_id, {
          [AuthConstants.FIREBASE_CLAIM_PENDING_STAGES]: [],
        });
      } catch (err) {
        throw err;
      }
    }

    await trx.commit();
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}

function getPendingStages(currentStage: AccountSetupStage): AccountSetupStage[] {
  switch (currentStage) {
    case AccountSetupStage.DOCUMENT_UPLOAD:
      return [
        AccountSetupStage.DOCUMENT_UPLOAD,
        AccountSetupStage.ADDING_NETWORK,
        AccountSetupStage.SUBSCRIPTION,
      ];
    case AccountSetupStage.ADDING_NETWORK:
      return [AccountSetupStage.ADDING_NETWORK, AccountSetupStage.SUBSCRIPTION];
    case AccountSetupStage.SUBSCRIPTION:
      return [AccountSetupStage.SUBSCRIPTION];
    case AccountSetupStage.PROFILE_SETUP:
      return [
        AccountSetupStage.PROFILE_SETUP,
        AccountSetupStage.DOCUMENT_UPLOAD,
        AccountSetupStage.ADDING_NETWORK,
        AccountSetupStage.SUBSCRIPTION,
      ];
    case AccountSetupStage.COMPLETED:
    default:
      return [];
  }
}

async function updateFirebaseClaims(providerId: string, newClaims: Record<string, any>) {
  const userRecord: UserRecord = await getAuth().getUser(providerId);
  const currentClaims = userRecord.customClaims || {};

  const updatedClaims = {
    ...currentClaims,
    ...newClaims,
  };

  await getAuth().setCustomUserClaims(providerId, updatedClaims);
}
