import type { Knex } from 'knex';

const TABLES = ['students', 'professionals', 'specialists'];

const QUALIFICATION_DETAILS_COL = 'qualification_details';

export async function up(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.json(QUALIFICATION_DETAILS_COL);
    });
  }
}

export async function down(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.dropColumn(QUALIFICATION_DETAILS_COL);
    });
  }
}
