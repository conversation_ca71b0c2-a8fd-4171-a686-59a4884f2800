import type { Knex } from 'knex';

const TABLES = ['organisations', 'professionals', 'specialists'];

const SHOWCASES_COL = 'showcases';

export async function up(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.json(SHOWCASES_COL);
    });
  }
}

export async function down(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.dropColumn(SHOWCASES_COL);
    });
  }
}
