import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('link_posts', (table) => {
      table.uuid('post_id').primary().references('id').inTable('posts').onDelete('CASCADE');
      table.text('link').notNullable();
      table.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      table.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
      CREATE TRIGGER link_posts_updated_at BEFORE UPDATE
      ON link_posts FOR EACH ROW EXECUTE PROCEDURE 
      update_updated_at_column();
      `,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.raw('DROP TRIGGER IF EXISTS link_posts_updated_at ON link_posts');
  return knex.schema.dropTable('link_posts');
}
