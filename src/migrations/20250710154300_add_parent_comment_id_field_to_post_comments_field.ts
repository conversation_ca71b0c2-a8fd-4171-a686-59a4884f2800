import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('post_comments', (table) => {
    table
      .uuid('parent_comment_id')
      .nullable()
      .references('id')
      .inTable('post_comments')
      .onDelete('CASCADE')
      .index('index_post_comments_on_parent_comment_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('post_comments', (table) => {
    table.dropColumn('parent_comment_id');
  });
}
