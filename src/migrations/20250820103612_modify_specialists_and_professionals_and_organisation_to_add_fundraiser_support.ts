import type { Knex } from 'knex';

const TABLES = ['organisations', 'professionals', 'specialists'];

const FUNDRAISER_COL = 'fundraiser';

export async function up(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.json(FUNDRAISER_COL);
    });
  }
}

export async function down(knex: Knex): Promise<void> {
  for (const table of TABLES) {
    await knex.schema.alterTable(table, (t) => {
      t.dropColumn(FUNDRAISER_COL);
    });
  }
}
