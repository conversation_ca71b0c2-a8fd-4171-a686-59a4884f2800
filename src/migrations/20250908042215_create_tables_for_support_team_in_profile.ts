import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('workspace_members', (t) => {
      t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_workspace_members_on_workspace_id');

      t.uuid('member_workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_workspace_members_on_member_workspace_id');

      t.integer('status').notNullable();

      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
      CREATE TRIGGER workspace_members_updated_at BEFORE UPDATE
      ON workspace_members FOR EACH ROW EXECUTE PROCEDURE 
      update_updated_at_column();
    `,
    )

    .createTable('workspace_invited_users', (t) => {
      t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));

      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_workspace_invited_users_on_workspace_id');

      t.text('email').notNullable();

      t.text('temp_name');
      t.text('temp_role');

      t.integer('status').notNullable();

      t.boolean('auto_connect_on_accept').notNullable();

      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
      CREATE TRIGGER workspace_invited_users_updated_at BEFORE UPDATE
      ON workspace_invited_users FOR EACH ROW EXECUTE PROCEDURE 
      update_updated_at_column();
    `,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('workspace_members').dropTable('workspace_invited_users');
}
