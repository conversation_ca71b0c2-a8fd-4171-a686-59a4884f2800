import { NestFactory } from '@nestjs/core';
import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Request, Response, NextFunction, json, raw } from 'express';
import helmet from 'helmet';
import * as admin from 'firebase-admin';
import cookieParser from 'cookie-parser';

import { AppModule } from './app.module';

import { CustomConfigService } from '@/config/configuration.service';

import { AuthModule } from '@/modules/auth/auth.module';
import { OnboardingModule } from './modules/onboarding/onboarding.module';
import { SubscriptionsModule } from '@/modules/subscriptions/subscriptions.module';
import { DropDownModule } from '@/modules/drop-downs/dropdowns.module';
import { EmployersModule } from './modules/employer/employer.module';
import { DocumentRequirementsModule } from './modules/document-requirements/document-requirements.module';
// import { SubtypesModule } from '@/modules/subtypes/subtypes.module';
import { UtilsModule } from '@/modules/utils/utils.module';
import { NetworkingModule } from '@/modules/networking/networking.module';
import { TagsModule } from '@/modules/tags/tags.module';
import { PostsModule } from '@/modules/posts/posts.module';
import { TagFollowModule } from '@/modules/tag-follow/tag-follow.module';
import { ProfileModule } from '@/modules/profile/profile.module';
import { StripeModule } from './modules/stripe/stripe.module';
import { LocationModule } from './modules/location/location.module';

// import { FileStorageModule } from '@/common/file-storage/file-storage.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    forceCloseConnections: true,
    logger: ['error', 'warn', 'debug', 'log'],
  });
  app.enableShutdownHooks();

  app.use(
    helmet({
      hidePoweredBy: true,
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false, // this is to allow redoc
    }),
  );

  app.use((_req: Request, res: Response, next: NextFunction) => {
    res.header('X-Robots-Tag', 'noindex, nofollow');
    next();
  });

  app.setGlobalPrefix('api');
  app.use('/api/v1/stripe/webhook', raw({ type: 'application/json' })); // For stripe webhook, it needs raw body to validate signature
  app.use(json({ limit: '25mb' }));
  app.use(cookieParser());
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  const configService = app.get(CustomConfigService);

  const firebaseServiceAc = configService.getFirebaseServiceAccount();

  admin.initializeApp({
    credential: admin.credential.cert(firebaseServiceAc),
  });

  const config = new DocumentBuilder()
    .addBearerAuth()
    .setTitle('minicardiac')
    .setDescription('The minicardiac API description')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    include: [
      AuthModule,
      OnboardingModule,
      SubscriptionsModule,
      DropDownModule,
      EmployersModule,
      DocumentRequirementsModule,
      UtilsModule,
      NetworkingModule,
      TagsModule,
      PostsModule,
      TagFollowModule,
      ProfileModule,
      StripeModule,
      LocationModule,
      // UsersModule,
      // WorkspacesModule,
      // WorkspaceUserAdminModule,
      // RolesModule,
      // PermissionsModule,
      // UserRolesModule,
      // FileStorageModule,
      // SubtypesModule,
    ],
    deepScanRoutes: true,
  });
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      defaultModelsExpandDepth: -1,
    },
  });

  const appConfig = configService.getAppConfig();

  const { port, feDashboardUrl, fePublicUrl } = appConfig;

  app.enableCors({
    credentials: true,
    origin: [
      feDashboardUrl,
      fePublicUrl,
      'https://minicardiac.com',
      'https://www.minicardiac.com', // Included www variant as well
    ],
  });

  await app.listen(port);

  Logger.log(`🚀 Application is running on: http://localhost:${port}`);
}

export default bootstrap();
