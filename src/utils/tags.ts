/**
 * Formats a tag string by:
 * 1. Removing non-alphanumeric characters
 * 2. Trimming whitespace
 * 3. Capitalizing the first letter of each word
 * 4. Removing spaces between words
 *
 * This ensures clean tags for storage and consistent formatting.
 *
 * Examples:
 * - "#cardiac surgeon" → "CardiacSurgeon"
 * - "  Tech2025  " → "Tech2025"
 * - "ai@startup" → "AiStartup"
 */
export const formatTag = (name: string): string => {
  // Handle edge case of empty or null input
  if (!name) return '';

  // Remove non-alphanumeric characters (except spaces for word separation)
  const cleaned = name.replace(/[^a-zA-Z0-9\s]/g, '').trim();

  // Split by one or more whitespace characters
  // Capitalize first letter of each word while preserving rest of word's casing
  // Then join without spaces
  return cleaned
    .split(/\s+/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
};
