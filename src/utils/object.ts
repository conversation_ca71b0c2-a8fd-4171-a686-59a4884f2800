export function removeUndefinedValues<T extends object>(obj: T): T {
  const cleanedObj = Object.entries(obj).reduce((acc, [key, value]) => {
    if (value !== undefined) {
      acc[key as keyof T] =
        typeof value === 'object' && value !== null && !Array.isArray(value)
          ? removeUndefinedValues(value)
          : value;
    }
    return acc;
  }, {} as Partial<T>);

  return cleanedObj as T;
}
