import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import {
  ISystemConfig,
  IDatabaseConfig,
  IAppConfig,
  ISMTPConfig,
  IEnviroment,
  IAwsS3Config,
  IMapboxConfig,
} from '@/interfaces/system';

@Injectable()
export class CustomConfigService {
  constructor(private readonly configService: ConfigService<ISystemConfig>) {}

  getDatabaseConfig(): IDatabaseConfig {
    return this.configService.get('database')!;
  }

  getAppConfig(): IAppConfig {
    return this.configService.get('app')!;
  }

  getAwsConfig(): IAwsS3Config {
    return this.configService.get('aws')!;
  }

  getSmtpConfig(): ISMTPConfig {
    return this.configService.get('smtp')!;
  }

  getIsProdCompiled(): boolean {
    return this.configService.get('isProdCompiled')!;
  }

  getFirebaseServiceAccount(): object {
    return this.configService.get('firebaseServiceAccount')!;
  }

  getFrontendHost(): string {
    return this.configService.get('frontendHost')!;
  }

  getEnvironment(): IEnviroment {
    return this.configService.get('environment')!;
  }

  getEnvironmentName(): string {
    return this.configService.get('environmentName')!;
  }

  getAppVersion(): string {
    return this.configService.get('appVersion')!;
  }

  getStripeConfig() {
    return this.configService.get('stripe')!;
  }

  getMapboxConfig(): IMapboxConfig {
    return this.configService.get('mapbox')!;
  }
}
