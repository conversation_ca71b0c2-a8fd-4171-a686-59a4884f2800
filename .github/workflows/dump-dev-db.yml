name: Dump Dev Database

on:
  workflow_dispatch:

jobs:
  dev_dump:
    runs-on: ubuntu-latest
    env:
      DOPPLER_TOKEN: ${{ secrets.DOPPLER_DEVELOP_TOKEN }}

    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Install CLI
        uses: dopplerhq/cli-action@v3

      - name: Fetch DATABASE_URL from Doppler and dump
        run: |
          eval $(doppler secrets download \
                    --no-file \
                    --format env \
                    | grep '^DB_=')
          export DATABASE_URL="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
          echo "DATABASE_URL=$DATABASE_URL" >> $GITHUB_ENV

          pg_dump \
            --clean \
            --no-owner \
            --no-privileges \
            -Fc \
            -v \
            -d "$DATABASE_URL" \
            -f dev_backup.dump

      - name: Upload dump artifact
        uses: actions/upload-artifact@v4
        with:
          name: dev_backup-backup
          path: dev_backup.dump